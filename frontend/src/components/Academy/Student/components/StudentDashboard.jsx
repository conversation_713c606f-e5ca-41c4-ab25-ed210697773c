import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Typography,
    Grid2 as Grid,
    Card,
    CardContent,
    CardMedia,
    Paper,
    List,
    ListItem,
    ListItemText,
    Chip,
    LinearProgress,
    Avatar,
    IconButton,
    Button,
    Alert,
    CircularProgress
} from '@mui/material';
import {
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    TrendingUp as TrendingUpIcon,
    Schedule as ScheduleIcon,
    PlayArrow as PlayIcon,
    Notifications as NotificationIcon
} from '@mui/icons-material';
import { elearningAPI } from '../../../../services';

function StudentDashboard({ user }) {
    const navigate = useNavigate();
    const [stats, setStats] = useState({
        enrolledCourses: 0,
        completedAssignments: 0,
        pendingQuizzes: 0,
        turboWarpProjects: 0
    });

    const [recentActivities, setRecentActivities] = useState([]);
    const [upcomingDeadlines, setUpcomingDeadlines] = useState([]);
    const [enrolledCourses, setEnrolledCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        loadDashboardData();
    }, []);

    const loadDashboardData = async () => {
        try {
            setLoading(true);

            // Load enrolled courses
            const enrolledData = await elearningAPI.studentAPI.getEnrolledCourses();
            setEnrolledCourses(enrolledData);

            // Update stats
            setStats({
                enrolledCourses: enrolledData.length,
                completedAssignments: 12,
                pendingQuizzes: 2,
                turboWarpProjects: 5
            });

            // Load sample activities and deadlines
            setRecentActivities([
                { id: 1, type: 'quiz', title: 'Hoàn thành bài kiểm tra Toán học', time: '1 giờ trước', status: 'completed', score: 85 },
                { id: 2, type: 'lesson', title: 'Xem bài giảng Vật lý - Chuyển động', time: '2 giờ trước', status: 'viewed' },
                { id: 3, type: 'assignment', title: 'Nộp bài tập Scratch - Game Pong', time: '1 ngày trước', status: 'submitted' },
            ]);

            setUpcomingDeadlines([
                { id: 1, title: 'Bài kiểm tra Vật lý giữa kỳ', course: 'Vật lý 9B', deadline: '2024-01-18 14:00', priority: 'high', type: 'quiz' },
                { id: 2, title: 'Nộp bài tập Toán học', course: 'Toán 8A', deadline: '2024-01-20 23:59', priority: 'medium', type: 'assignment' },
                { id: 3, title: 'Dự án Scratch cuối kỳ', course: 'Tin học 7C', deadline: '2024-01-25 23:59', priority: 'low', type: 'turbowarp' },
            ]);

        } catch (err) {
            setError('Không thể tải dữ liệu dashboard');
            console.error('Dashboard data error:', err);
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, icon, color, subtitle }) => (
        <Card sx={{ height: '100%', borderRadius: '10px' }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                        <Typography color="textSecondary" gutterBottom variant="overline">
                            {title}
                        </Typography>
                        <Typography variant="h4" component="div" sx={{ color: color }}>
                            {value}
                        </Typography>
                        {subtitle && (
                            <Typography variant="body2" color="textSecondary">
                                {subtitle}
                            </Typography>
                        )}
                    </Box>
                    <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
                        {icon}
                    </Avatar>
                </Box>
            </CardContent>
        </Card>
    );

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'success';
            case 'viewed': return 'info';
            case 'submitted': return 'warning';
            default: return 'default';
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'error';
            case 'medium': return 'warning';
            case 'low': return 'success';
            default: return 'default';
        }
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'quiz': return <QuizIcon sx={{ fontSize: 16 }} />;
            case 'assignment': return <AssignmentIcon sx={{ fontSize: 16 }} />;
            case 'turbowarp': return <TurboWarpIcon sx={{ fontSize: 16 }} />;
            default: return <ScheduleIcon sx={{ fontSize: 16 }} />;
        }
    };

    return (
        <Box sx={{ p: 3, display: "flex", justifyContent: "space-between", flexDirection: "row", }}>
            <Box sx={{ flexGrow: 1, width: "100%" }}>
                {/* Welcome Section */}
                <Box sx={{ mb: 4 }}>
                    <Typography variant="h4" gutterBottom>
                        Chào {user?.first_name || 'bạn'}! 👋
                    </Typography>
                    <Typography variant="body1" color="textSecondary">
                        Hôm nay là {new Date().toLocaleDateString('vi-VN', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        })}. Hãy tiếp tục hành trình học tập của bạn!
                    </Typography>
                </Box>

                {/* Statistics Cards */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <StatCard
                            title="Khóa học đang học"
                            value={stats.enrolledCourses}
                            icon={<SchoolIcon />}
                            color="#2e7d32"
                        />
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <StatCard
                            title="Bài tập đã hoàn thành"
                            value={stats.completedAssignments}
                            icon={<AssignmentIcon />}
                            color="#1976d2"
                        />
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <StatCard
                            title="Bài kiểm tra chờ làm"
                            value={stats.pendingQuizzes}
                            icon={<QuizIcon />}
                            color="#f57c00"
                        />
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <StatCard
                            title="Dự án TurboWarp"
                            value={stats.turboWarpProjects}
                            icon={<TurboWarpIcon />}
                            color="#7b1fa2"
                        />
                    </Grid>
                </Grid>

                {/* Content Grid */}
                <Grid container spacing={3}>
                    {/* Current Courses */}
                    <Grid size={{ xs: 12, md: 8 }}>
                        <Paper sx={{ p: 3, mb: 3, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                <Typography variant="h6">
                                    Khóa học đã đăng ký ({enrolledCourses.length})
                                </Typography>
                                <Button
                                    variant="outlined"
                                    onClick={() => navigate('/e-learning')}
                                    sx={{ textTransform: 'none' }}
                                >
                                    Khám phá thêm
                                </Button>
                            </Box>

                            {loading ? (
                                <Box display="flex" justifyContent="center" py={4}>
                                    <CircularProgress />
                                </Box>
                            ) : error ? (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {error}
                                </Alert>
                            ) : enrolledCourses.length === 0 ? (
                                <Box textAlign="center" py={4}>
                                    <SchoolIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                                    <Typography variant="h6" color="textSecondary" gutterBottom>
                                        Chưa có khóa học nào
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                                        Hãy khám phá và đăng ký các khóa học thú vị
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        onClick={() => navigate('/e-learning')}
                                        sx={{ bgcolor: '#a435f0', '&:hover': { bgcolor: '#8710d8' } }}
                                    >
                                        Khám phá khóa học
                                    </Button>
                                </Box>
                            ) : (
                                <Grid container spacing={2}>
                                    {enrolledCourses.map((enrollment) => (
                                        <Grid xs={12} md={6} key={enrollment.id}>
                                            <Card
                                                sx={{
                                                    height: '100%',
                                                    borderRadius: '10px',
                                                    cursor: 'pointer',
                                                    '&:hover': {
                                                        boxShadow: 4
                                                    }
                                                }}
                                                onClick={() => navigate(`/e-learning/course/${enrollment.course.id}`)}
                                            >
                                                {enrollment.course.thumbnail && (
                                                    <CardMedia
                                                        component="img"
                                                        height="120"
                                                        image={enrollment.course.thumbnail}
                                                        alt={enrollment.course.title}
                                                    />
                                                )}
                                                <CardContent>
                                                    <Typography variant="h6" gutterBottom sx={{ fontSize: '1rem' }}>
                                                        {enrollment.course.title}
                                                    </Typography>
                                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                                        {enrollment.course.instructor?.name || 'BeE Learning'}
                                                    </Typography>
                                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                                        {enrollment.course.subject?.name}
                                                    </Typography>

                                                    <Box sx={{ mb: 2 }}>
                                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                            <Typography variant="body2">
                                                                Tiến độ: {Math.round(enrollment.progress_percentage)}%
                                                            </Typography>
                                                            <Typography variant="body2" color="textSecondary">
                                                                {enrollment.completed_lessons}/{enrollment.total_lessons || enrollment.course.total_lessons} bài
                                                            </Typography>
                                                        </Box>
                                                        <LinearProgress
                                                            variant="determinate"
                                                            value={enrollment.progress_percentage}
                                                            sx={{
                                                                height: 8,
                                                                borderRadius: 5,
                                                                backgroundColor: '#e0e0e0',
                                                                '& .MuiLinearProgress-bar': {
                                                                    borderRadius: 5,
                                                                    backgroundColor: enrollment.status === 'completed' ? '#4caf50' : '#a435f0'
                                                                }
                                                            }}
                                                        />
                                                    </Box>

                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                        <Chip
                                                            label={enrollment.status === 'completed' ? 'Hoàn thành' : 'Đang học'}
                                                            color={enrollment.status === 'completed' ? 'success' : 'primary'}
                                                            size="small"
                                                        />
                                                        <Button
                                                            variant="contained"
                                                            size="small"
                                                            startIcon={<PlayIcon />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                navigate(`/e-learning/course/${enrollment.course.id}`);
                                                            }}
                                                            sx={{
                                                                bgcolor: '#a435f0',
                                                                '&:hover': { bgcolor: '#8710d8' }
                                                            }}
                                                        >
                                                            {enrollment.status === 'completed' ? 'Xem lại' : 'Tiếp tục'}
                                                        </Button>
                                                    </Box>
                                                </CardContent>
                                            </Card>
                                        </Grid>
                                    ))}
                                </Grid>
                            )}
                        </Paper>

                        {/* Recent Activities */}
                        <Paper sx={{ p: 3, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                <Typography variant="h6">
                                    Hoạt động gần đây
                                </Typography>
                                <IconButton>
                                    <TrendingUpIcon />
                                </IconButton>
                            </Box>
                            <List>
                                {recentActivities.map((activity) => (
                                    <ListItem key={activity.id} divider>
                                        <ListItemText
                                            primary={activity.title}
                                            secondary={activity.time}
                                        />
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            {activity.score && (
                                                <Chip
                                                    label={`${activity.score} điểm`}
                                                    color="success"
                                                    size="small"
                                                />
                                            )}
                                            <Chip
                                                label={activity.status}
                                                color={getStatusColor(activity.status)}
                                                size="small"
                                            />
                                        </Box>
                                    </ListItem>
                                ))}
                            </List>
                            <Box sx={{ mt: 2, textAlign: 'center' }}>
                                <Button variant="outlined" size="small">
                                    Xem tất cả hoạt động
                                </Button>
                            </Box>
                        </Paper>
                    </Grid>

                    {/* Upcoming Deadlines */}
                    <Grid size={{ xs: 12, md: 4 }}>
                        <Paper sx={{ p: 3, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                <Typography variant="h6">
                                    Deadline sắp tới
                                </Typography>
                                <IconButton>
                                    <NotificationIcon />
                                </IconButton>
                            </Box>
                            <List>
                                {upcomingDeadlines.map((deadline) => (
                                    <ListItem key={deadline.id} divider>
                                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                                            {getTypeIcon(deadline.type)}
                                        </Box>
                                        <ListItemText
                                            primary={deadline.title}
                                            secondary={`${deadline.course} • ${deadline.deadline}`}
                                        />
                                        <Chip
                                            label={deadline.priority}
                                            color={getPriorityColor(deadline.priority)}
                                            size="small"
                                        />
                                    </ListItem>
                                ))}
                            </List>
                            <Box sx={{ mt: 2, textAlign: 'center' }}>
                                <Button variant="outlined" size="small">
                                    Xem lịch đầy đủ
                                </Button>
                            </Box>
                        </Paper>
                    </Grid>
                </Grid>

                {/* Quick Actions */}
                <Paper sx={{ p: 3, mt: 3, borderRadius: '10px' }}>
                    <Typography variant="h6" gutterBottom>
                        Thao tác nhanh
                    </Typography>
                    <Grid container spacing={2}>
                        <Grid>
                            <Button
                                variant="contained"
                                startIcon={<PlayIcon />}
                                sx={{ mr: 1, bgcolor: '#2e7d32', borderRadius: '10px' }}
                            >
                                Tiếp tục học bài
                            </Button>
                        </Grid>
                        <Grid item>
                            <Button
                                variant="outlined"
                                startIcon={<QuizIcon />}
                                sx={{ mr: 1, borderRadius: '10px' }}
                            >
                                Làm bài kiểm tra
                            </Button>
                        </Grid>
                        <Grid item>
                            <Button
                                variant="outlined"
                                startIcon={<TurboWarpIcon />}
                                sx={{ mr: 1, borderRadius: '10px' }}
                            >
                                Làm bài TurboWarp
                            </Button>
                        </Grid>
                        <Grid item>
                            <Button
                                variant="outlined"
                                startIcon={<SchoolIcon />}
                                sx={{ mr: 1, borderRadius: '10px' }}
                            >
                                Xem khóa học mới
                            </Button>
                        </Grid>
                    </Grid>
                </Paper>
            </Box>
        </Box>
    );
}

export default StudentDashboard;
