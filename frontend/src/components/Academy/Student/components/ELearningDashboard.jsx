import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Rating
} from '@mui/material';
import {
  School as SchoolIcon,
  PlayArrow as PlayIcon,
  ShoppingCart as CartIcon,
  Star as StarIcon,
  AccessTime as TimeIcon,
  People as PeopleIcon,
  // Certificate as CertificateIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import CardMembershipIcon from '@mui/icons-material/CardMembership';
import { elearningAPI } from '../../../../services';

const ELearningDashboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const [dashboardData, setDashboardData] = useState(null);
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [availableCourses, setAvailableCourses] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    subject: '',
    grade: '',
    difficulty: '',
    price_range: '',
    search: ''
  });

  useEffect(() => {
    loadDashboardData();
    loadEnrolledCourses();
    loadAvailableCourses();
    loadSubjects();
    loadGrades();
  }, []);

  useEffect(() => {
    if (tabValue === 1) {
      loadAvailableCourses();
    }
  }, [filters, tabValue]);

  const loadDashboardData = async () => {
    try {
      const data = await elearningAPI.studentAPI.getDashboard();
      setDashboardData(data);
    } catch (err) {
      setError('Không thể tải dữ liệu dashboard');
      console.error('Dashboard error:', err);
    }
  };

  const loadEnrolledCourses = async () => {
    try {
      const data = await elearningAPI.studentAPI.getEnrolledCourses();
      setEnrolledCourses(data);
    } catch (err) {
      setError('Không thể tải khóa học đã đăng ký');
      console.error('Enrolled courses error:', err);
    }
  };

  const loadAvailableCourses = async () => {
    try {
      const data = await elearningAPI.studentAPI.getAvailableCourses(filters);
      setAvailableCourses(data);
      setLoading(false);
    } catch (err) {
      setError('Không thể tải khóa học có sẵn');
      console.error('Available courses error:', err);
      setLoading(false);
    }
  };

  const loadSubjects = async () => {
    try {
      const data = await elearningAPI.getSubjects();
      setSubjects(data);
    } catch (err) {
      console.error('Subjects error:', err);
    }
  };

  const loadGrades = async () => {
    try {
      const data = await elearningAPI.getGrades();
      setGrades(data);
    } catch (err) {
      console.error('Grades error:', err);
    }
  };

  const handlePurchaseCourse = async (courseId) => {
    try {
      await elearningAPI.studentAPI.purchaseCourse(courseId, {
        payment_method: 'credit_card'
      });
      loadEnrolledCourses();
      loadAvailableCourses();
      loadDashboardData();
    } catch (err) {
      setError('Không thể mua khóa học');
      console.error('Purchase course error:', err);
    }
  };

  const formatPrice = (price) => {
    if (price === 0) return 'Miễn phí';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getDifficultyColor = (difficulty) => {
    const colors = {
      'beginner': 'success',
      'intermediate': 'warning',
      'advanced': 'error',
      'expert': 'secondary'
    };
    return colors[difficulty] || 'default';
  };

  const getDifficultyLabel = (difficulty) => {
    const labels = {
      'beginner': 'Cơ bản',
      'intermediate': 'Trung bình',
      'advanced': 'Nâng cao',
      'expert': 'Chuyên sâu'
    };
    return labels[difficulty] || difficulty;
  };

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes} phút`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} giờ`;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Dashboard Stats */}
      {dashboardData && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <SchoolIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{dashboardData.enrolled_courses}</Typography>
                    <Typography color="textSecondary">Khóa học đang học</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CardMembershipIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{dashboardData.completed_courses}</Typography>
                    <Typography color="textSecondary">Đã hoàn thành</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TimeIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{formatDuration(dashboardData.total_time_spent)}</Typography>
                    <Typography color="textSecondary">Thời gian học</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <TrendingUpIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h4">{dashboardData.avg_score}%</Typography>
                    <Typography color="textSecondary">Điểm trung bình</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label="Khóa học của tôi" />
          <Tab label="Khám phá khóa học" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Grid container spacing={3}>
          {enrolledCourses.map((enrollment) => (
            <Grid item xs={12} md={6} lg={4} key={enrollment.id}>
              <Card>
                {enrollment.course.thumbnail && (
                  <CardMedia
                    component="img"
                    height="140"
                    image={enrollment.course.thumbnail}
                    alt={enrollment.course.title}
                  />
                )}
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {enrollment.course.title}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {enrollment.course.description.substring(0, 100)}...
                  </Typography>

                  <Box display="flex" gap={1} mb={2}>
                    <Chip
                      label={enrollment.course.subject.name}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={getDifficultyLabel(enrollment.course.difficulty)}
                      size="small"
                      color={getDifficultyColor(enrollment.course.difficulty)}
                    />
                  </Box>

                  <Box mb={2}>
                    <Typography variant="body2" gutterBottom>
                      Tiến độ: {enrollment.progress_percentage}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={enrollment.progress_percentage}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>

                  <Typography variant="body2" gutterBottom>
                    <strong>Thời gian học:</strong> {formatDuration(enrollment.total_time_spent)}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Bài học hoàn thành:</strong> {enrollment.completed_lessons}/{enrollment.course.total_lessons}
                  </Typography>

                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<PlayIcon />}
                    sx={{ mt: 2 }}
                  >
                    Tiếp tục học
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {tabValue === 1 && (
        <Box>
          {/* Filters */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    placeholder="Tìm kiếm khóa học..."
                    value={filters.search}
                    onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Môn học</InputLabel>
                    <Select
                      value={filters.subject}
                      onChange={(e) => setFilters({ ...filters, subject: e.target.value })}
                    >
                      <MenuItem value="">Tất cả</MenuItem>
                      {subjects.map((subject) => (
                        <MenuItem key={subject.id} value={subject.name}>
                          {subject.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Khối lớp</InputLabel>
                    <Select
                      value={filters.grade}
                      onChange={(e) => setFilters({ ...filters, grade: e.target.value })}
                    >
                      <MenuItem value="">Tất cả</MenuItem>
                      {grades.map((grade) => (
                        <MenuItem key={grade.id} value={grade.name}>
                          {grade.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Độ khó</InputLabel>
                    <Select
                      value={filters.difficulty}
                      onChange={(e) => setFilters({ ...filters, difficulty: e.target.value })}
                    >
                      <MenuItem value="">Tất cả</MenuItem>
                      <MenuItem value="beginner">Cơ bản</MenuItem>
                      <MenuItem value="intermediate">Trung bình</MenuItem>
                      <MenuItem value="advanced">Nâng cao</MenuItem>
                      <MenuItem value="expert">Chuyên sâu</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={2}>
                  <FormControl fullWidth>
                    <InputLabel>Giá</InputLabel>
                    <Select
                      value={filters.price_range}
                      onChange={(e) => setFilters({ ...filters, price_range: e.target.value })}
                    >
                      <MenuItem value="">Tất cả</MenuItem>
                      <MenuItem value="free">Miễn phí</MenuItem>
                      <MenuItem value="under_500k">Dưới 500k</MenuItem>
                      <MenuItem value="500k_1m">500k - 1M</MenuItem>
                      <MenuItem value="over_1m">Trên 1M</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={() => setFilters({
                      subject: '',
                      grade: '',
                      difficulty: '',
                      price_range: '',
                      search: ''
                    })}
                  >
                    Xóa bộ lọc
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Available Courses */}
          <Grid container spacing={3}>
            {availableCourses.map((course) => (
              <Grid item xs={12} md={6} lg={4} key={course.id}>
                <Card>
                  {course.thumbnail && (
                    <CardMedia
                      component="img"
                      height="140"
                      image={course.thumbnail}
                      alt={course.title}
                    />
                  )}
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {course.description.substring(0, 100)}...
                    </Typography>

                    <Box display="flex" gap={1} mb={2}>
                      <Chip
                        label={course.subject.name}
                        size="small"
                        color="primary"
                      />
                      <Chip
                        label={getDifficultyLabel(course.difficulty)}
                        size="small"
                        color={getDifficultyColor(course.difficulty)}
                      />
                    </Box>

                    <Box display="flex" alignItems="center" mb={1}>
                      <Rating value={course.average_rating} readOnly size="small" />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        ({course.total_reviews})
                      </Typography>
                    </Box>

                    <Typography variant="body2" gutterBottom>
                      <strong>Giá:</strong> {formatPrice(course.price)}
                      {course.discount_percentage > 0 && (
                        <Chip
                          label={`-${course.discount_percentage}%`}
                          size="small"
                          color="error"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Thời lượng:</strong> {course.duration}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Học viên:</strong> {course.enrolled_count}
                    </Typography>

                    <Button
                      fullWidth
                      variant="contained"
                      startIcon={course.is_free ? <PlayIcon /> : <CartIcon />}
                      sx={{ mt: 2 }}
                      onClick={() => handlePurchaseCourse(course.id)}
                    >
                      {course.is_free ? 'Học ngay' : 'Mua khóa học'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default ELearningDashboard;
