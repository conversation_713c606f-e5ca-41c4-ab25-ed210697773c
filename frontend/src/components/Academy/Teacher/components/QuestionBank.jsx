import React, { useState, useEffect } from 'react';
import { elearningAPI } from '../../../../services';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    FormControlLabel,
    InputLabel,
    Select,
    MenuItem,
    Checkbox,
    Card,
    CardContent,
    Fab,
    Tooltip,
    Alert,
    Snackbar
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    QuestionAnswer as QuestionIcon
} from '@mui/icons-material';

function QuestionBank({ user }) {
    const [questions, setQuestions] = useState([]);
    const [filteredQuestions, setFilteredQuestions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingQuestion, setEditingQuestion] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterSubject, setFilterSubject] = useState('');
    const [filterDifficulty, setFilterDifficulty] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [subjects, setSubjects] = useState([]);
    const [grades, setGrades] = useState([]);
    const [loadingSubjects, setLoadingSubjects] = useState(false);

    // Form state
    const [formData, setFormData] = useState({
        question: '',
        options: ['', '', '', ''],
        correctAnswers: [], // Changed to array for multiple correct answers
        subject: '',
        grade: '',
        difficulty: 'medium',
        type: 'multiple_choice',
        points: 1,
        explanation: '',
        tags: ''
    });

    const difficulties = [
        { value: 'easy', label: 'Dễ', color: 'success' },
        { value: 'medium', label: 'Trung bình', color: 'warning' },
        { value: 'hard', label: 'Khó', color: 'error' }
    ];

    useEffect(() => {
        loadQuestions();
        loadSubjectsAndGrades();
    }, []);

    const loadSubjectsAndGrades = async () => {
        try {
            setLoadingSubjects(true);
            const [subjectsData, gradesData] = await Promise.all([
                elearningAPI.teacherAPI.getSubjects(),
                elearningAPI.teacherAPI.getGrades()
            ]);
            setSubjects(subjectsData);
            setGrades(gradesData);
        } catch (error) {
            console.error('Error loading subjects and grades:', error);
            // Fallback to some default subjects if API fails
            setSubjects([]);
            setGrades([]);
        } finally {
            setLoadingSubjects(false);
        }
    };

    const loadQuestions = async () => {
        try {
            setLoading(true);
            const questionsData = await elearningAPI.teacherAPI.getQuestions();
            setQuestions(questionsData);
            setFilteredQuestions(questionsData);
        } catch (error) {
            console.error('Error loading questions:', error);
            showSnackbar('Không thể tải ngân hàng câu hỏi', 'error');
            // Fallback to empty array
            setQuestions([]);
            setFilteredQuestions([]);
        } finally {
            setLoading(false);
        }
    };

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };

    useEffect(() => {
        let filtered = questions;

        if (searchTerm) {
            filtered = filtered.filter(q =>
                q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                q.tags.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterSubject) {
            filtered = filtered.filter(q => q.subject === filterSubject);
        }

        if (filterDifficulty) {
            filtered = filtered.filter(q => q.difficulty === filterDifficulty);
        }

        setFilteredQuestions(filtered);
        setPage(0);
    }, [questions, searchTerm, filterSubject, filterDifficulty]);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (question = null) => {
        if (question) {
            setEditingQuestion(question);
            setFormData({
                question: question.question_text,
                options: [...question.options],
                correctAnswers: question.correct_answers || [],
                subject: question.subject?.id || question.subject || '',
                grade: question.grade?.id || question.grade || '',
                difficulty: question.difficulty,
                type: question.question_type || 'multiple_choice',
                points: question.points || 1,
                explanation: question.explanation,
                tags: question.tags
            });
        } else {
            setEditingQuestion(null);
            setFormData({
                question: '',
                options: ['', '', '', ''],
                correctAnswers: [],
                subject: '',
                grade: '',
                difficulty: 'medium',
                type: 'multiple_choice',
                points: 1,
                explanation: '',
                tags: ''
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingQuestion(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleOptionChange = (index, value) => {
        const newOptions = [...formData.options];
        newOptions[index] = value;
        setFormData(prev => ({
            ...prev,
            options: newOptions
        }));
    };

    const handleSaveQuestion = async () => {
        if (!formData.question || formData.options.some(opt => !opt)) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin câu hỏi và các đáp án!',
                severity: 'error'
            });
            return;
        }

        if (formData.type === 'multiple_choice' && formData.correctAnswers.length === 0) {
            setSnackbar({
                open: true,
                message: 'Vui lòng chọn ít nhất một đáp án đúng!',
                severity: 'error'
            });
            return;
        }

        try {
            const questionData = {
                question_text: formData.question,
                question_type: formData.type,
                difficulty: formData.difficulty,
                points: formData.points,
                options: formData.options,
                subject: formData.subject ? parseInt(formData.subject) : null,
                grade: formData.grade ? parseInt(formData.grade) : null,
                correct_answers: formData.correctAnswers,
                explanation: formData.explanation || '',
                tags: Array.isArray(formData.tags) ? formData.tags :
                    (formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [])
            };
            if (editingQuestion) {
                const updatedQuestion = await elearningAPI.teacherAPI.updateQuestion(
                    editingQuestion.id, questionData
                );
                setQuestions(prev => prev.map(q => q.id === editingQuestion.id ? updatedQuestion : q));
                setSnackbar({
                    open: true,
                    message: 'Cập nhật câu hỏi thành công!',
                    severity: 'success'
                });
            } else {
                const newQuestion = await elearningAPI.teacherAPI.createQuestion(questionData);
                setQuestions(prev => [newQuestion, ...prev]);
                setSnackbar({
                    open: true,
                    message: 'Thêm câu hỏi thành công!',
                    severity: 'success'
                });
            }

            handleCloseDialog();
        } catch (error) {
            console.error('Error saving question:', error);
            setSnackbar({
                open: true,
                message: 'Có lỗi xảy ra khi lưu câu hỏi',
                severity: 'error'
            });
        }
    };

    const handleDeleteQuestion = async (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')) {
            try {
                await elearningAPI.teacherAPI.deleteQuestion(id);
                setQuestions(prev => prev.filter(q => q.id !== id));
                setSnackbar({
                    open: true,
                    message: 'Xóa câu hỏi thành công!',
                    severity: 'success'
                });
            } catch (error) {
                console.error('Error deleting question:', error);
                setSnackbar({
                    open: true,
                    message: 'Có lỗi xảy ra khi xóa câu hỏi',
                    severity: 'error'
                });
            }
        }
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyInfo = difficulties.find(d => d.value === difficulty);
        return (
            <Chip
                label={difficultyInfo?.label || difficulty}
                color={difficultyInfo?.color || 'default'}
                size="small"
            />
        );
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Ngân hàng câu hỏi
                </Typography>
                <Button
                    sx={{ borderRadius: "10px", display: { sm: "none", md: "flex" } }}
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Thêm câu hỏi
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng số câu hỏi
                            </Typography>
                            <Typography variant="h4">
                                {questions.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Môn học
                            </Typography>
                            <Typography variant="h4">
                                {new Set(questions.map(q => q.subject)).size}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Câu hỏi dễ
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {questions.filter(q => q.difficulty === 'easy').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Câu hỏi khó
                            </Typography>
                            <Typography variant="h4" color="error.main">
                                {questions.filter(q => q.difficulty === 'hard').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: "10px" }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={{ xs: 12, md: 4 }}>
                        <TextField
                            size="small"
                            fullWidth
                            placeholder="Tìm kiếm câu hỏi..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Môn học</InputLabel>
                            <Select
                                value={filterSubject}
                                onChange={(e) => setFilterSubject(e.target.value)}
                                label="Môn học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {subjects.map(subject => (
                                    <MenuItem key={subject.id} value={subject.id}>{subject.name}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Độ khó</InputLabel>
                            <Select
                                value={filterDifficulty}
                                onChange={(e) => setFilterDifficulty(e.target.value)}
                                label="Độ khó"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {difficulties.map(difficulty => (
                                    <MenuItem key={difficulty.value} value={difficulty.value}>
                                        {difficulty.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 2 }}>
                        <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterSubject('');
                                setFilterDifficulty('');
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Questions Table */}
            <TableContainer component={Paper} sx={{ borderRadius: "10px" }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Câu hỏi</TableCell>
                            <TableCell>Môn học</TableCell>
                            <TableCell>Lớp học</TableCell>
                            <TableCell>Độ khó</TableCell>
                            <TableCell>Tags</TableCell>
                            <TableCell>Ngày tạo</TableCell>
                            <TableCell align="center">Thao tác</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {filteredQuestions
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((question) => (
                                <TableRow key={question.id}>
                                    <TableCell>
                                        <Typography variant="body2" sx={{ maxWidth: 300 }}>
                                            {question.question_text.length > 100
                                                ? `${question.question_text.substring(0, 100)}...`
                                                : question.question_text
                                            }
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Chip label={subjects.find(s => s.id === question.subject)?.name} variant="outlined" size="small" />
                                    </TableCell>
                                    <TableCell>
                                        <Chip label={grades.find(g => g.id === question.grade)?.name} variant="outlined" size="small" />
                                    </TableCell>
                                    <TableCell>
                                        {getDifficultyChip(question.difficulty)}
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="textSecondary">
                                            {question.tags}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>{question.created_at.split('T')[0]}</TableCell>
                                    <TableCell align="center">
                                        <Tooltip title="Chỉnh sửa">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleOpenDialog(question)}
                                                color="primary"
                                            >
                                                <EditIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Xóa">
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteQuestion(question.id)}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    component="div"
                    count={filteredQuestions.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage="Số hàng mỗi trang:"
                />
            </TableContainer>

            {/* Add/Edit Question Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingQuestion ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Câu hỏi"
                                multiline
                                rows={3}
                                value={formData.question}
                                onChange={(e) => handleFormChange('question', e.target.value)}
                            />
                        </Grid>

                        {formData.options.map((option, index) => (
                            <Grid size={{ xs: 12 }} key={index}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={formData.correctAnswers.includes(index)}
                                                onChange={(e) => {
                                                    const newCorrectAnswers = [...formData.correctAnswers];
                                                    if (e.target.checked) {
                                                        newCorrectAnswers.push(index);
                                                    } else {
                                                        const idx = newCorrectAnswers.indexOf(index);
                                                        if (idx > -1) newCorrectAnswers.splice(idx, 1);
                                                    }
                                                    setFormData(prev => ({
                                                        ...prev,
                                                        correctAnswers: newCorrectAnswers
                                                    }));
                                                }}
                                            />
                                        }
                                        label={`${String.fromCharCode(65 + index)}.`}
                                        sx={{ minWidth: '50px' }}
                                    />
                                    <TextField
                                        fullWidth
                                        label={`Đáp án ${String.fromCharCode(65 + index)}`}
                                        value={option}
                                        onChange={(e) => handleOptionChange(index, e.target.value)}
                                        color={formData.correctAnswers.includes(index) ? 'success' : 'primary'}
                                    />
                                </Box>
                            </Grid>
                        ))}

                        <Grid size={{ xs: 12 }}>
                            <Typography variant="body2" color="textSecondary">
                                Chọn một hoặc nhiều đáp án đúng bằng cách tick vào checkbox bên trái mỗi đáp án.
                                Đã chọn: {formData.correctAnswers.length} đáp án
                            </Typography>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Môn học</InputLabel>
                                <Select
                                    value={formData.subject}
                                    onChange={(e) => handleFormChange('subject', e.target.value)}
                                    label="Môn học"
                                >
                                    {subjects.map(subject => (
                                        <MenuItem key={subject.id} value={subject.id}>{subject.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Lớp học</InputLabel>
                                <Select
                                    value={formData.grade}
                                    onChange={(e) => handleFormChange('grade', e.target.value)}
                                    label="Lớp học"
                                >
                                    <MenuItem value="">Chọn lớp học</MenuItem>
                                    {grades.map(grade => (
                                        <MenuItem key={grade.id} value={grade.id}>{grade.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Độ khó</InputLabel>
                                <Select
                                    value={formData.difficulty}
                                    onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                    label="Độ khó"
                                >
                                    {difficulties.map(difficulty => (
                                        <MenuItem key={difficulty.value} value={difficulty.value}>
                                            {difficulty.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Giải thích"
                                multiline
                                rows={2}
                                value={formData.explanation}
                                onChange={(e) => handleFormChange('explanation', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tags (phân cách bằng dấu phẩy)"
                                value={formData.tags}
                                onChange={(e) => handleFormChange('tags', e.target.value)}
                                placeholder="ví dụ: toán học, cơ bản, phép cộng"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: "10px" }}>Hủy</Button>
                    <Button onClick={handleSaveQuestion} variant="contained" sx={{ borderRadius: "10px" }}>
                        {editingQuestion ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Floating Action Button */}
            <Fab
                color="primary"
                aria-label="add"
                sx={{ position: 'fixed', bottom: 16, right: 16, display: { sm: 'block', md: 'none' } }}
                onClick={() => handleOpenDialog()}
            >
                <AddIcon />
            </Fab>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default QuestionBank;
