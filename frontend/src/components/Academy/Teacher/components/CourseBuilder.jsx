import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import RichTextEditor from './RichTextEditor';
import { elearningAPI } from '../../../../services';
import { useTeacherLayout } from '../contexts/TeacherLayoutContext';
import {
    Box,
    Typography,
    Button,
    Paper,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    ListItemSecondaryAction,
    Divider,
    Stepper,
    Step,
    StepLabel,
    StepContent,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Tab,
    Tabs,
    Checkbox,
    FormControlLabel,
    CircularProgress,
    LinearProgress,
    Badge,
    Fade,
    Slide,
    alpha,
    InputAdornment
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    DragIndicator as DragIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    ExpandMore as ExpandMoreIcon,
    Save as SaveIcon,
    Preview as PreviewIcon,
    ArrowUpward as UpIcon,
    ArrowDownward as DownIcon,
    ArrowBack as ArrowBackIcon,
    Build as BuildIcon,
    Search as SearchIcon
} from '@mui/icons-material';
import { grey } from '@mui/material/colors';
import CustomButton, { beeColors } from '../../../Common/CustomButton';
import CustomTextField from '../../../Common/CustomTextField';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`builder-tabpanel-${index}`}
            aria-labelledby={`builder-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function CourseBuilder({ courseId, onClose }) {
    const navigate = useNavigate();
    const { enterFullScreen, exitFullScreen } = useTeacherLayout();
    const [course, setCourse] = useState(null);
    const [content, setContent] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [dialogType, setDialogType] = useState('lesson'); // lesson, quiz, turbowarp
    const [editingItem, setEditingItem] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [loading, setLoading] = useState(false);
    const [availableQuestions, setAvailableQuestions] = useState([]);
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const [loadingQuestions, setLoadingQuestions] = useState(false);
    const [reloadingContent, setReloadingContent] = useState(false);

    // Question filter states
    const [questionSearchTerm, setQuestionSearchTerm] = useState('');
    const [questionTypeFilter, setQuestionTypeFilter] = useState('');
    const [questionPointsFilter, setQuestionPointsFilter] = useState('');

    // Form state for content items
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        contentType: 'text', // text, video, file
        content: '',
        videoUrl: '',
        fileName: '',
        fileUrl: '',
        duration: 30,
        questions: [],
        requiredFeatures: [],
        gradingCriteria: []
    });

    const contentTypes = [
        { value: 'text', label: 'Nội dung văn bản', icon: <TextIcon /> },
        { value: 'video', label: 'Video', icon: <VideoIcon /> },
        { value: 'file', label: 'Tài liệu', icon: <FileIcon /> }
    ];

    useEffect(() => {
        if (courseId) {
            loadCourseData();
        }
    }, [courseId]);

    // Enter full-screen mode when component mounts
    useEffect(() => {
        enterFullScreen('Course Builder');
        return () => {
            exitFullScreen();
        };
    }, [enterFullScreen, exitFullScreen]);

    const handleClose = () => {
        exitFullScreen();
        onClose();
    };

    const loadCourseData = async () => {
        try {
            setLoading(true);
            const [courseData, contentData] = await Promise.all([
                elearningAPI.teacherAPI.getCourse(courseId),
                elearningAPI.teacherAPI.getCourseContent(courseId)
            ]);

            setCourse(courseData);
            setContentFromAPI(contentData);
        } catch (error) {
            console.error('Error loading course data:', error);
            showSnackbar('Không thể tải dữ liệu khóa học', 'error');
            // Fallback to mock data for development
            // loadMockData();
        } finally {
            setLoading(false);
        }
    };

    const loadCourseContent = async (showLoadingState = false) => {
        try {
            if (showLoadingState) {
                setLoading(true);
            } else {
                setReloadingContent(true);
            }
            const contentData = await elearningAPI.teacherAPI.getCourseContent(courseId);
            setContentFromAPI(contentData);
        } catch (error) {
            console.error('Error loading course content:', error);
            showSnackbar('Không thể tải nội dung khóa học', 'error');
        } finally {
            if (showLoadingState) {
                setLoading(false);
            } else {
                setReloadingContent(false);
            }
        }
    };

    const setContentFromAPI = (contentData) => {
        // Combine all content types with type markers and sort by order
        const allContent = [
            ...(contentData.lessons || []).map(item => ({ ...item, _type: 'lesson' })),
            ...(contentData.quizzes || []).map(item => ({ ...item, _type: 'quiz' })),
            ...(contentData.assignments || []).map(item => ({ ...item, _type: 'assignment' }))
        ].sort((a, b) => a.order - b.order);

        setContent(allContent);
    };

    // Helper function to extract question IDs from quiz questions
    const extractQuestionIds = (quizQuestions, availableQuestions) => {
        if (!quizQuestions || !Array.isArray(quizQuestions)) {
            return [];
        }

        return quizQuestions.map(q => {
            // If question is just an ID (number or string)
            if (typeof q === 'number') {
                return q;
            }
            if (typeof q === 'string' && !isNaN(parseInt(q))) {
                return parseInt(q);
            }

            // If question is an object
            if (typeof q === 'object' && q !== null) {
                // Check for question_bank_data.id first (this is the correct ID)
                if (q.question_bank_data && q.question_bank_data.id !== undefined) {
                    const id = typeof q.question_bank_data.id === 'string' ? parseInt(q.question_bank_data.id) : q.question_bank_data.id;
                    return id;
                }

                // If question has id property, use it
                if (q.id !== undefined) {
                    return typeof q.id === 'string' ? parseInt(q.id) : q.id;
                }
                // If question has question_id property, use it
                if (q.question_id !== undefined) {
                    return typeof q.question_id === 'string' ? parseInt(q.question_id) : q.question_id;
                }

                // Fallback - try to match by question_text
                if (q.question_text && availableQuestions) {
                    const matchingQuestion = availableQuestions.find(aq =>
                        aq.question_text === q.question_text
                    );
                    return matchingQuestion ? matchingQuestion.id : null;
                }
            }

            return null;
        }).filter(id => id !== null && !isNaN(id));
    };

    const loadQuestions = async () => {
        try {
            setLoadingQuestions(true);

            // Load questions from API
            const questions = await elearningAPI.teacherAPI.getQuestions();
            if (questions && questions.length > 0) {
                setAvailableQuestions(questions);
            } else {
                console.error('No questions in database, using mock questions for demo');
                // Fallback to mock questions for demo
                const mockQuestions = [];
                setAvailableQuestions(mockQuestions);
            }
        } catch (error) {
            console.error('Error loading questions:', error);
            showSnackbar('Không thể tải danh sách câu hỏi', 'error');
        } finally {
            setLoadingQuestions(false);
        }
    };

    const handleQuestionToggle = (questionId) => {
        setSelectedQuestions(prev => {
            if (prev.includes(questionId)) {
                return prev.filter(id => id !== questionId);
            } else {
                return [...prev, questionId];
            }
        });
    };

    const handleSelectAllQuestions = (checked) => {
        if (checked) {
            setSelectedQuestions(filteredQuestions.map(q => q.id));
        } else {
            setSelectedQuestions([]);
        }
    };

    // Filter questions based on search term, type, and points
    const filteredQuestions = availableQuestions.filter(question => {
        const matchesSearch = question.question_text.toLowerCase().includes(questionSearchTerm.toLowerCase());
        const matchesType = !questionTypeFilter || question.question_type === questionTypeFilter;
        const matchesPoints = !questionPointsFilter || question.points.toString() === questionPointsFilter;

        return matchesSearch && matchesType && matchesPoints;
    });

    // Get unique question types and points for filter options
    const questionTypes = [...new Set(availableQuestions.map(q => q.question_type))];
    const questionPoints = [...new Set(availableQuestions.map(q => q.points))].sort((a, b) => a - b);

    const handleAddContent = (type) => {
        setDialogType(type);
        setEditingItem(null);
        setFormData({
            title: '',
            description: '',
            contentType: 'text',
            content: '',
            videoUrl: '',
            fileName: '',
            fileUrl: '',
            duration: 30,
            questions: [],
            requiredFeatures: [],
            gradingCriteria: []
        });
        setSelectedQuestions([]);
        setTabValue(0);

        // Load questions if opening quiz dialog
        if (type === 'quiz') {
            loadQuestions();
        }

        setOpenDialog(true);
    };

    const handleEditContent = (item) => {
        // Determine type based on _type marker or item properties
        let itemType = 'lesson';

        if (item._type) {
            // Use _type marker from API data
            itemType = item._type === 'assignment' ? 'turbowarp' : item._type;
        } else if (item.content_type) {
            // This is a lesson from API
            itemType = 'lesson';
        } else if (item.assignment_type) {
            // This is an assignment from API
            itemType = 'turbowarp';
        } else if (item.max_attempts !== undefined && item.passing_score !== undefined) {
            // This is a quiz from API
            itemType = 'quiz';
        } else if (item.type) {
            // This is mock data
            itemType = item.type;
        }

        setDialogType(itemType);
        setEditingItem(item);

        if (itemType === 'lesson') {
            setFormData({
                title: item.title,
                description: item.description,
                contentType: item.content_type || 'text',
                content: item.content || '',
                videoUrl: item.video_url || '',
                fileName: item.fileName || '',
                fileUrl: item.fileUrl || '',
                duration: item.duration || 30,
                questions: item.questions || [],
                requiredFeatures: item.requiredFeatures || [],
                gradingCriteria: item.gradingCriteria || []
            });
        } else if (itemType === 'quiz') {
            setFormData({
                title: item.title,
                description: item.description,
                duration: item.duration || 30,
                questions: item.questions || [],
                contentType: 'text',
                content: '',
                videoUrl: '',
                fileName: '',
                fileUrl: '',
                requiredFeatures: [],
                gradingCriteria: []
            });

            // Load questions and set selected questions for editing
            loadQuestions().then(() => {

                // Check for questions in different possible properties
                let questionsToProcess = null;
                if (item.questions && item.questions.length > 0) {
                    questionsToProcess = item.questions;
                } else if (item.question_ids && item.question_ids.length > 0) {
                    questionsToProcess = item.question_ids;
                }

                if (questionsToProcess) {
                    const questionIds = extractQuestionIds(questionsToProcess, availableQuestions);
                    setSelectedQuestions(questionIds);
                } else {
                    setSelectedQuestions([]);
                }
            });
        } else if (itemType === 'turbowarp') {
            setFormData({
                title: item.title,
                description: item.description,
                requiredFeatures: item.required_features || [],
                gradingCriteria: item.grading_criteria || [],
                duration: 30,
                questions: [],
                contentType: 'text',
                content: '',
                videoUrl: '',
                fileName: '',
                fileUrl: ''
            });
        }

        setTabValue(0);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingItem(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveContent = async () => {
        if (!formData.title || !formData.description) {
            showSnackbar('Vui lòng điền đầy đủ thông tin!', 'error');
            return;
        }

        // Validate quiz questions
        if (dialogType === 'quiz' && selectedQuestions.length === 0) {
            showSnackbar('Vui lòng chọn ít nhất một câu hỏi cho bài kiểm tra!', 'error');
            return;
        }

        try {
            setLoading(true);

            if (dialogType === 'lesson') {
                const lessonData = {
                    title: formData.title,
                    description: formData.description,
                    content_type: formData.contentType,
                    content: formData.content || '',
                    video_url: formData.videoUrl || '',
                    duration: formData.duration || 0,
                    order: editingItem ? editingItem.order : content.length + 1,
                    is_preview: false,
                    is_required: true
                };

                if (editingItem) {
                    // Update existing lesson
                    await elearningAPI.teacherAPI.updateLesson(courseId, editingItem.id, lessonData);
                    showSnackbar('Cập nhật bài học thành công!', 'success');
                } else {
                    // Create new lesson
                    await elearningAPI.teacherAPI.createLesson(courseId, lessonData);
                    showSnackbar('Thêm bài học thành công!', 'success');
                }

                // Reload course content to get fresh data
                await loadCourseContent();
            } else if (dialogType === 'quiz') {
                const quizData = {
                    title: formData.title,
                    description: formData.description,
                    duration: formData.duration || 30,
                    max_attempts: 3,
                    passing_score: 70,
                    shuffle_questions: false,
                    show_results_immediately: true,
                    allow_review: true,
                    is_required: true,
                    order: editingItem ? editingItem.order : content.length + 1,
                    question_ids: selectedQuestions
                };

                if (editingItem) {
                    // Update existing quiz
                    await elearningAPI.teacherAPI.updateQuiz(courseId, editingItem.id, quizData);
                    showSnackbar('Cập nhật bài kiểm tra thành công!', 'success');
                } else {
                    // Create new quiz
                    await elearningAPI.teacherAPI.createQuiz(courseId, quizData);
                    showSnackbar('Thêm bài kiểm tra thành công!', 'success');
                }

                // Reload course content to get fresh data
                await loadCourseContent();
            } else if (dialogType === 'turbowarp') {
                const assignmentData = {
                    title: formData.title,
                    description: formData.description,
                    assignment_type: 'turbowarp',
                    required_features: formData.requiredFeatures || [],
                    grading_criteria: formData.gradingCriteria || [],
                    starter_project_url: '',
                    max_score: 100,
                    due_date: null,
                    allow_late_submission: true,
                    max_attempts: 5,
                    order: editingItem ? editingItem.order : content.length + 1
                };

                if (editingItem) {
                    // Update existing assignment
                    await elearningAPI.teacherAPI.updateAssignment(courseId, editingItem.id, assignmentData);
                    showSnackbar('Cập nhật bài tập TurboWarp thành công!', 'success');
                } else {
                    // Create new assignment
                    await elearningAPI.teacherAPI.createAssignment(courseId, assignmentData);
                    showSnackbar('Thêm bài tập TurboWarp thành công!', 'success');
                }

                // Reload course content to get fresh data
                await loadCourseContent();
            }

            handleCloseDialog();
        } catch (error) {
            console.error('Error saving content:', error);
            showSnackbar('Có lỗi xảy ra khi lưu nội dung', 'error');
        } finally {
            setLoading(false);
        }
    };

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };



    const handleDeleteContent = async (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa nội dung này?')) {
            try {
                setLoading(true);
                const item = content.find(c => c.id === id);
                if (item) {
                    if (item._type === 'lesson' || item.content_type) {
                        // This is a lesson from API
                        await elearningAPI.teacherAPI.deleteLesson(courseId, id);
                    } else if (item._type === 'quiz' || (item.max_attempts !== undefined && item.passing_score !== undefined)) {
                        // This is a quiz from API
                        await elearningAPI.teacherAPI.deleteQuiz(courseId, id);
                    } else if (item._type === 'assignment' || item.assignment_type) {
                        // This is an assignment from API
                        await elearningAPI.teacherAPI.deleteAssignment(courseId, id);
                    }
                }

                showSnackbar('Xóa nội dung thành công!', 'success');

                // Reload course content to get fresh data
                await loadCourseContent();
            } catch (error) {
                console.error('Error deleting content:', error);
                showSnackbar('Có lỗi xảy ra khi xóa nội dung', 'error');
            } finally {
                setLoading(false);
            }
        }
    };

    const [movingItems, setMovingItems] = useState(new Set());

    // Function to save content order to backend
    const saveContentOrder = async (contentArray) => {
        // Group items by type for batch updates
        const lessons = contentArray.filter(item => item._type === 'lesson' || item.content_type);
        const quizzes = contentArray.filter(item => item._type === 'quiz' || (item.max_attempts !== undefined && item.passing_score !== undefined));
        const assignments = contentArray.filter(item => item._type === 'assignment' || item.assignment_type);
        const updatePromises = [];

        // Update lessons
        for (const lesson of lessons) {
            if (lesson.id) {
                // Send full lesson data with updated order
                const updateData = {
                    title: lesson.title,
                    description: lesson.description,
                    content_type: lesson.content_type || 'text',
                    content: lesson.content || '',
                    video_url: lesson.video_url || '',
                    duration: lesson.duration || 0,
                    order: lesson.order,
                    is_preview: lesson.is_preview || false,
                    is_required: lesson.is_required !== undefined ? lesson.is_required : true
                };
                updatePromises.push(
                    elearningAPI.teacherAPI.updateLesson(courseId, lesson.id, updateData)
                        .catch(error => {
                            console.error(`Error updating lesson ${lesson.id}:`, error);
                            console.error('Error details:', error.response?.data);
                            throw error;
                        })
                );
            }
        }

        // Update quizzes
        for (const quiz of quizzes) {
            if (quiz.id) {
                // Send full quiz data with updated order
                const updateData = {
                    title: quiz.title,
                    description: quiz.description,
                    duration: quiz.duration || 30,
                    max_attempts: quiz.max_attempts || 3,
                    passing_score: quiz.passing_score || 70,
                    shuffle_questions: quiz.shuffle_questions || false,
                    show_results_immediately: quiz.show_results_immediately !== undefined ? quiz.show_results_immediately : true,
                    allow_review: quiz.allow_review !== undefined ? quiz.allow_review : true,
                    is_required: quiz.is_required !== undefined ? quiz.is_required : true,
                    order: quiz.order,
                    question_ids: quiz.question_ids || []
                };
                updatePromises.push(
                    elearningAPI.teacherAPI.updateQuiz(courseId, quiz.id, updateData)
                        .catch(error => {
                            console.error(`Error updating quiz ${quiz.id}:`, error);
                            console.error('Error details:', error.response?.data);
                            throw error;
                        })
                );
            }
        }

        // Update assignments
        for (const assignment of assignments) {
            if (assignment.id) {
                // Send full assignment data with updated order
                const updateData = {
                    title: assignment.title,
                    description: assignment.description,
                    assignment_type: assignment.assignment_type || 'turbowarp',
                    required_features: assignment.required_features || [],
                    grading_criteria: assignment.grading_criteria || [],
                    starter_project_url: assignment.starter_project_url || '',
                    max_score: assignment.max_score || 100,
                    due_date: assignment.due_date || null,
                    allow_late_submission: assignment.allow_late_submission !== undefined ? assignment.allow_late_submission : true,
                    max_attempts: assignment.max_attempts || 5,
                    order: assignment.order
                };
                updatePromises.push(
                    elearningAPI.teacherAPI.updateAssignment(courseId, assignment.id, updateData)
                        .catch(error => {
                            console.error(`Error updating assignment ${assignment.id}:`, error);
                            console.error('Error details:', error.response?.data);
                            throw error;
                        })
                );
            }
        }

        // Execute all updates
        if (updatePromises.length > 0) {
            await Promise.all(updatePromises);
        } else {
            console.error('No items to update');
        }
    };

    const handleMoveContent = async (currentIndex, direction) => {
        // Validate bounds
        if (
            (direction === 'up' && currentIndex === 0) ||
            (direction === 'down' && currentIndex === content.length - 1) ||
            currentIndex < 0 ||
            currentIndex >= content.length
        ) {
            console.error('Move blocked - out of bounds');
            return;
        }

        // Prevent multiple moves at once
        if (movingItems.size > 0) {
            console.error('Move blocked - another move in progress');
            return;
        }

        const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        // Add moving animation state
        setMovingItems(new Set([currentIndex, targetIndex]));

        // Optional: Add subtle haptic feedback for mobile
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }

        // Wait for animation to start
        await new Promise(resolve => setTimeout(resolve, 150));

        const newContent = [...content];

        // Swap items
        [newContent[currentIndex], newContent[targetIndex]] = [newContent[targetIndex], newContent[currentIndex]];

        // Update order property for all items
        newContent.forEach((item, index) => {
            item.order = index + 1;
        });

        setContent(newContent);

        // Clear animation state after content update
        setTimeout(() => {
            setMovingItems(new Set());
        }, 300);

        // Save order changes to backend
        try {
            await saveContentOrder(newContent);
            showSnackbar('Đã cập nhật thứ tự thành công!', 'success');
        } catch (error) {
            console.error('Error saving content order:', error);
            console.error('Full error object:', error);
            console.error('Error response:', error.response);
            console.error('Error response data:', error.response?.data);

            // Show specific error message
            let errorMessage = 'Có lỗi khi lưu thứ tự. Vui lòng thử lại.';

            if (error.response?.data) {
                if (typeof error.response.data === 'string') {
                    errorMessage = error.response.data;
                } else if (error.response.data.detail) {
                    errorMessage = error.response.data.detail;
                } else if (error.response.data.message) {
                    errorMessage = error.response.data.message;
                } else if (error.response.data.error) {
                    errorMessage = error.response.data.error;
                } else {
                    // Show first validation error if available
                    const firstError = Object.values(error.response.data)[0];
                    if (Array.isArray(firstError)) {
                        errorMessage = firstError[0];
                    } else if (typeof firstError === 'string') {
                        errorMessage = firstError;
                    }
                }
            }

            showSnackbar(errorMessage, 'error');

            // Revert to original order on error
            await loadCourseContent();
        }
    };

    const getContentIcon = (item) => {
        // Use _type marker first (from API data)
        if (item._type) {
            switch (item._type) {
                case 'lesson': return <LessonIcon />;
                case 'quiz': return <QuizIcon />;
                case 'assignment': return <TurboWarpIcon />;
                default: return <LessonIcon />;
            }
        }

        // Fallback: Detect type from item properties
        if (item.content_type) {
            return <LessonIcon />; // This is a lesson
        } else if (item.questions !== undefined || item.max_attempts !== undefined) {
            return <QuizIcon />; // This is a quiz
        } else if (item.assignment_type) {
            return <TurboWarpIcon />; // This is an assignment
        } else if (item.type) {
            // Fallback to type property for mock data
            switch (item.type) {
                case 'lesson': return <LessonIcon />;
                case 'quiz': return <QuizIcon />;
                case 'turbowarp': return <TurboWarpIcon />;
                default: return <LessonIcon />;
            }
        }
        return <LessonIcon />; // Default
    };

    const getContentTypeLabel = (item) => {
        if (item._type) {
            switch (item._type) {
                case 'lesson': return 'Bài học';
                case 'quiz': return 'Bài kiểm tra';
                case 'assignment': return 'Bài tập TurboWarp';
                default: return 'Bài học';
            }
        }

        // Fallback detection
        if (item.content_type) return 'Bài học';
        if (item.max_attempts !== undefined) return 'Bài kiểm tra';
        if (item.assignment_type) return 'Bài tập TurboWarp';
        if (item.type === 'quiz') return 'Bài kiểm tra';
        if (item.type === 'turbowarp') return 'Bài tập TurboWarp';

        return 'Bài học';
    };

    const getContentTypeIcon = (contentType) => {
        const typeInfo = contentTypes.find(t => t.value === contentType);
        return typeInfo?.icon || <TextIcon />;
    };

    const renderContentForm = () => {
        switch (dialogType) {
            case 'lesson':
                return (
                    <Box>
                        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                            <Tab label="Thông tin cơ bản" />
                            <Tab label="Nội dung" />
                        </Tabs>

                        <TabPanel value={tabValue} index={0}>
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12 }}>
                                    <TextField
                                        fullWidth
                                        label="Tiêu đề bài học *"
                                        value={formData.title}
                                        onChange={(e) => handleFormChange('title', e.target.value)}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12 }}>
                                    <TextField
                                        fullWidth
                                        label="Mô tả"
                                        multiline
                                        rows={2}
                                        value={formData.description}
                                        onChange={(e) => handleFormChange('description', e.target.value)}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <TextField
                                        fullWidth
                                        label="Thời lượng (phút)"
                                        type="number"
                                        value={formData.duration}
                                        onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, sm: 6 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>Loại nội dung</InputLabel>
                                        <Select
                                            value={formData.contentType}
                                            onChange={(e) => handleFormChange('contentType', e.target.value)}
                                            label="Loại nội dung"
                                        >
                                            {contentTypes.map((type, index) => (
                                                <MenuItem key={index} value={type.value}>
                                                    {type.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                            </Grid>
                        </TabPanel>

                        <TabPanel value={tabValue} index={1}>
                            {formData.contentType === 'text' && (
                                <RichTextEditor
                                    // label="Nội dung bài học"
                                    value={formData.content}
                                    onChange={(data) => handleFormChange('content', data)}
                                    height={400}
                                />
                            )}
                            {formData.contentType === 'video' && (
                                <TextField
                                    fullWidth
                                    label="URL Video"
                                    value={formData.videoUrl}
                                    onChange={(e) => handleFormChange('videoUrl', e.target.value)}
                                    placeholder="https://youtube.com/watch?v=..."
                                />
                            )}
                            {formData.contentType === 'file' && (
                                <Box>
                                    <TextField
                                        fullWidth
                                        label="Tên file"
                                        value={formData.fileName}
                                        onChange={(e) => handleFormChange('fileName', e.target.value)}
                                        sx={{ mb: 2 }}
                                    />
                                    <Button variant="outlined" component="label">
                                        Chọn file
                                        <input type="file" hidden />
                                    </Button>
                                </Box>
                            )}
                        </TabPanel>
                    </Box>
                );

            case 'quiz':
                return (
                    <Grid container spacing={2}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài kiểm tra *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Thời gian (phút)"
                                type="number"
                                value={formData.duration}
                                onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={2}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>
                        {/* Question Selection */}
                        <Grid size={{ xs: 12 }}>
                            <Typography variant="h6" gutterBottom>
                                Chọn câu hỏi cho bài kiểm tra
                            </Typography>

                            {loadingQuestions ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                                    <CircularProgress />
                                    <Typography sx={{ ml: 2 }}>Đang tải câu hỏi...</Typography>
                                </Box>
                            ) : availableQuestions.length > 0 ? (
                                <Box>
                                    {/* Filter Controls */}
                                    <Box sx={{
                                        p: 3,
                                        mb: 3,
                                        backgroundColor: beeColors.background.paper,
                                        borderRadius: '16px',
                                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`
                                    }}>
                                        <Typography variant="h6" sx={{ mb: 2, color: beeColors.neutral.main, fontWeight: 600 }}>
                                            🔍 Lọc câu hỏi
                                        </Typography>

                                        <Grid container spacing={2}>
                                            <Grid size={{ xs: 12, md: 6 }}>
                                                <CustomTextField
                                                    fullWidth
                                                    size="small"
                                                    label="Tìm kiếm câu hỏi"
                                                    placeholder="Nhập nội dung câu hỏi..."
                                                    value={questionSearchTerm}
                                                    onChange={(e) => setQuestionSearchTerm(e.target.value)}
                                                    slotProps={{
                                                        input: {
                                                            startAdornment: (
                                                                <InputAdornment position="start">
                                                                    <SearchIcon sx={{ color: beeColors.neutral.light }} />
                                                                </InputAdornment>
                                                            ),
                                                        }
                                                    }}
                                                />
                                            </Grid>

                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth size="small">
                                                    <InputLabel>Loại câu hỏi</InputLabel>
                                                    <Select
                                                        value={questionTypeFilter}
                                                        onChange={(e) => setQuestionTypeFilter(e.target.value)}
                                                        label="Loại câu hỏi"
                                                        sx={{ borderRadius: '12px' }}
                                                    >
                                                        <MenuItem value="">Tất cả</MenuItem>
                                                        {questionTypes.map((type) => (
                                                            <MenuItem key={type} value={type}>
                                                                {type === 'multiple_choice' ? 'Trắc nghiệm' :
                                                                    type === 'true_false' ? 'Đúng/Sai' :
                                                                        type === 'short_answer' ? 'Trả lời ngắn' :
                                                                            type === 'essay' ? 'Tự luận' : type}
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>

                                            <Grid size={{ xs: 12, md: 3 }}>
                                                <FormControl fullWidth size="small">
                                                    <InputLabel>Điểm số</InputLabel>
                                                    <Select
                                                        value={questionPointsFilter}
                                                        onChange={(e) => setQuestionPointsFilter(e.target.value)}
                                                        label="Điểm số"
                                                        sx={{ borderRadius: '12px' }}
                                                    >
                                                        <MenuItem value="">Tất cả</MenuItem>
                                                        {questionPoints.map((points) => (
                                                            <MenuItem key={points} value={points.toString()}>
                                                                {points} điểm
                                                            </MenuItem>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                            </Grid>
                                        </Grid>

                                        {/* Filter Summary */}
                                        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                                            <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                                Hiển thị {filteredQuestions.length} / {availableQuestions.length} câu hỏi
                                            </Typography>

                                            {(questionSearchTerm || questionTypeFilter || questionPointsFilter) && (
                                                <CustomButton
                                                    size="small"
                                                    variant="outlined"
                                                    onClick={() => {
                                                        setQuestionSearchTerm('');
                                                        setQuestionTypeFilter('');
                                                        setQuestionPointsFilter('');
                                                    }}
                                                    sx={{
                                                        minWidth: 'auto',
                                                        borderColor: alpha(beeColors.neutral.main, 0.3),
                                                        color: beeColors.neutral.main
                                                    }}
                                                >
                                                    Xóa bộ lọc
                                                </CustomButton>
                                            )}
                                        </Box>
                                    </Box>

                                    {/* Select All Checkbox */}
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={selectedQuestions.length === filteredQuestions.length && filteredQuestions.length > 0}
                                                indeterminate={selectedQuestions.length > 0 && selectedQuestions.length < filteredQuestions.length}
                                                onChange={(e) => handleSelectAllQuestions(e.target.checked)}
                                            />
                                        }
                                        label={`Chọn tất cả (${selectedQuestions.length}/${filteredQuestions.length})`}
                                        sx={{ mb: 2 }}
                                    />

                                    {/* Question List */}
                                    <Paper sx={{ maxHeight: 400, overflow: 'auto', p: 1 }}>
                                        {filteredQuestions.length === 0 ? (
                                            <Box sx={{
                                                textAlign: 'center',
                                                py: 4,
                                                px: 2
                                            }}>
                                                <Typography variant="h6" sx={{ color: beeColors.neutral.light, mb: 1 }}>
                                                    Không tìm thấy câu hỏi
                                                </Typography>
                                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                                    Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
                                                </Typography>
                                            </Box>
                                        ) : (
                                            filteredQuestions.map((question, index) => (
                                                <Card key={index} sx={{ mb: 1, border: selectedQuestions.includes(question.id) ? '2px solid #1976d2' : '1px solid #e0e0e0' }}>
                                                    <CardContent sx={{ py: 1 }}>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={selectedQuestions.includes(question.id)}
                                                                    onChange={() => handleQuestionToggle(question.id)}
                                                                />
                                                            }
                                                            label={
                                                                <Box>
                                                                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                                                        {question.question_text}
                                                                    </Typography>
                                                                    <Typography variant="caption" color="textSecondary">
                                                                        Loại: {question.question_type} | Điểm: {question.points}
                                                                    </Typography>
                                                                </Box>
                                                            }
                                                            sx={{ width: '100%', m: 0 }}
                                                        />
                                                    </CardContent>
                                                </Card>
                                            ))
                                        )}
                                    </Paper>
                                </Box>
                            ) : (
                                <Alert severity="warning">
                                    Chưa có câu hỏi nào trong ngân hàng câu hỏi. Vui lòng tạo câu hỏi trước khi tạo bài kiểm tra.
                                </Alert>
                            )}
                        </Grid>
                    </Grid>
                );

            case 'turbowarp':
                return (
                    <Grid container spacing={2}>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài tập *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={3}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tính năng yêu cầu"
                                multiline
                                rows={2}
                                value={formData.requiredFeatures.join('\n')}
                                onChange={(e) => handleFormChange('requiredFeatures', e.target.value.split('\n').filter(f => f.trim()))}
                                placeholder="Mỗi tính năng một dòng..."
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tiêu chí chấm điểm"
                                multiline
                                rows={2}
                                value={formData.gradingCriteria.join('\n')}
                                onChange={(e) => handleFormChange('gradingCriteria', e.target.value.split('\n').filter(c => c.trim()))}
                                placeholder="Mỗi tiêu chí một dòng..."
                            />
                        </Grid>
                    </Grid>
                );

            default:
                return null;
        }
    };

    if (!course) {
        return <Typography>Đang tải...</Typography>;
    }

    return (
        <Box sx={{
            minHeight: '100vh',
            backgroundColor: beeColors.background.main,
            p: { xs: 2, sm: 3 }
        }}>
            {/* Header */}
            <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                mb: 4,
                flexWrap: 'wrap',
                gap: 2
            }}>
                <Box sx={{ flex: 1, minWidth: 300 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Box sx={{
                            width: 48,
                            height: 48,
                            borderRadius: '12px',
                            background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.primary.light} 100%)`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: `0 4px 16px ${alpha(beeColors.primary.main, 0.3)}`
                        }}>
                            <BuildIcon sx={{ color: 'white', fontSize: 24 }} />
                        </Box>
                        <Box>
                            <Typography
                                variant="h4"
                                sx={{
                                    fontWeight: 700,
                                    color: beeColors.neutral.main,
                                    mb: 0.5
                                }}
                            >
                                Xây dựng khóa học 🚀
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: beeColors.neutral.light,
                                    fontWeight: 500
                                }}
                            >
                                {course.title}
                            </Typography>
                        </Box>
                    </Box>

                    {/* Course Progress */}
                    <Box sx={{
                        p: 3,
                        backgroundColor: beeColors.background.paper,
                        borderRadius: '16px',
                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                        boxShadow: `0 2px 8px ${alpha(beeColors.neutral.main, 0.05)}`
                    }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                            <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                Tiến độ xây dựng
                            </Typography>
                            <Typography variant="body2" sx={{ color: beeColors.primary.main, fontWeight: 600 }}>
                                {content.length} nội dung
                            </Typography>
                        </Box>
                        <LinearProgress
                            variant="determinate"
                            value={Math.min((content.length / 5) * 100, 100)}
                            sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: alpha(beeColors.primary.main, 0.1),
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 4,
                                    background: `linear-gradient(90deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`
                                }
                            }}
                        />
                    </Box>
                </Box>

                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <CustomButton
                        variant="outlined"
                        onClick={handleClose}
                        startIcon={<ArrowBackIcon />}
                        sx={{
                            borderColor: alpha(beeColors.neutral.main, 0.3),
                            color: beeColors.neutral.main,
                            '&:hover': {
                                borderColor: beeColors.neutral.main,
                                backgroundColor: alpha(beeColors.neutral.main, 0.05)
                            }
                        }}
                    >
                        Quay lại
                    </CustomButton>
                    <CustomButton
                        variant="contained"
                        startIcon={<PreviewIcon />}
                        onClick={() => {
                            navigate(`/teacher/courses/${courseId}/preview`);
                        }}
                        sx={{
                            background: `linear-gradient(135deg, ${beeColors.secondary.main} 0%, ${beeColors.secondary.light} 100%)`,
                            '&:hover': {
                                background: `linear-gradient(135deg, ${beeColors.secondary.dark} 0%, ${beeColors.secondary.main} 100%)`
                            }
                        }}
                    >
                        Xem trước
                    </CustomButton>
                    {/* <CustomButton
                        variant="contained"
                        startIcon={<SaveIcon />}
                        onClick={() => { }}
                        disabled={loading}
                    >
                        {loading ? 'Đang lưu...' : 'Lưu khóa học'}
                    </CustomButton> */}
                </Box>
            </Box>

            {/* Add Content Section */}
            <Box sx={{
                p: 4,
                backgroundColor: beeColors.background.paper,
                borderRadius: '20px',
                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                boxShadow: `0 4px 16px ${alpha(beeColors.neutral.main, 0.08)}`,
                mb: 4
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                    <Box sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '10px',
                        background: `linear-gradient(135deg, ${beeColors.accent.main} 0%, ${beeColors.accent.light} 100%)`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        <AddIcon sx={{ color: 'white', fontSize: 20 }} />
                    </Box>
                    <Box>
                        <Typography
                            variant="h5"
                            sx={{
                                fontWeight: 600,
                                color: beeColors.neutral.main,
                                mb: 0.5
                            }}
                        >
                            Thêm nội dung mới
                        </Typography>
                        <Typography
                            variant="body2"
                            sx={{ color: beeColors.neutral.light }}
                        >
                            Chọn loại nội dung bạn muốn thêm vào khóa học
                        </Typography>
                    </Box>
                </Box>

                <Grid container spacing={3}>
                    <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                        <Card sx={{
                            p: 3,
                            borderRadius: '16px',
                            border: `2px solid ${alpha(beeColors.primary.main, 0.2)}`,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                borderColor: beeColors.primary.main,
                                boxShadow: `0 8px 24px ${alpha(beeColors.primary.main, 0.2)}`,
                                transform: 'translateY(-4px)'
                            }
                        }}
                            onClick={() => handleAddContent('lesson')}
                        >
                            <Box sx={{ textAlign: 'center' }}>
                                <Box sx={{
                                    width: 64,
                                    height: 64,
                                    borderRadius: '16px',
                                    background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.primary.light} 100%)`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mx: 'auto',
                                    mb: 2,
                                    boxShadow: `0 4px 16px ${alpha(beeColors.primary.main, 0.3)}`
                                }}>
                                    <LessonIcon sx={{ color: 'white', fontSize: 32 }} />
                                </Box>
                                <Typography variant="h6" sx={{ fontWeight: 600, color: beeColors.neutral.main, mb: 1 }}>
                                    Bài học
                                </Typography>
                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                    Tạo bài học với nội dung text, video hoặc file
                                </Typography>
                            </Box>
                        </Card>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                        <Card sx={{
                            p: 3,
                            borderRadius: '16px',
                            border: `2px solid ${alpha(beeColors.secondary.main, 0.2)}`,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                borderColor: beeColors.secondary.main,
                                boxShadow: `0 8px 24px ${alpha(beeColors.secondary.main, 0.2)}`,
                                transform: 'translateY(-4px)'
                            }
                        }}
                            onClick={() => handleAddContent('quiz')}
                        >
                            <Box sx={{ textAlign: 'center' }}>
                                <Box sx={{
                                    width: 64,
                                    height: 64,
                                    borderRadius: '16px',
                                    background: `linear-gradient(135deg, ${beeColors.secondary.main} 0%, ${beeColors.secondary.light} 100%)`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mx: 'auto',
                                    mb: 2,
                                    boxShadow: `0 4px 16px ${alpha(beeColors.secondary.main, 0.3)}`
                                }}>
                                    <QuizIcon sx={{ color: 'white', fontSize: 32 }} />
                                </Box>
                                <Typography variant="h6" sx={{ fontWeight: 600, color: beeColors.neutral.main, mb: 1 }}>
                                    Bài kiểm tra
                                </Typography>
                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                    Tạo bài kiểm tra từ ngân hàng câu hỏi
                                </Typography>
                            </Box>
                        </Card>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 4 }}>
                        <Card sx={{
                            p: 3,
                            borderRadius: '16px',
                            border: `2px solid ${alpha(beeColors.accent.main, 0.2)}`,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                borderColor: beeColors.accent.main,
                                boxShadow: `0 8px 24px ${alpha(beeColors.accent.main, 0.2)}`,
                                transform: 'translateY(-4px)'
                            }
                        }}
                            onClick={() => handleAddContent('turbowarp')}
                        >
                            <Box sx={{ textAlign: 'center' }}>
                                <Box sx={{
                                    width: 64,
                                    height: 64,
                                    borderRadius: '16px',
                                    background: `linear-gradient(135deg, ${beeColors.accent.main} 0%, ${beeColors.accent.light} 100%)`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mx: 'auto',
                                    mb: 2,
                                    boxShadow: `0 4px 16px ${alpha(beeColors.accent.main, 0.3)}`
                                }}>
                                    <TurboWarpIcon sx={{ color: beeColors.accent.contrastText, fontSize: 32 }} />
                                </Box>
                                <Typography variant="h6" sx={{ fontWeight: 600, color: beeColors.neutral.main, mb: 1 }}>
                                    Bài tập TurboWarp
                                </Typography>
                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                    Tạo bài tập lập trình với TurboWarp
                                </Typography>
                            </Box>
                        </Card>
                    </Grid>
                </Grid>
            </Box>

            {/* Course Content */}
            <Box sx={{
                backgroundColor: beeColors.background.paper,
                borderRadius: '20px',
                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                boxShadow: `0 4px 16px ${alpha(beeColors.neutral.main, 0.08)}`,
                overflow: 'hidden'
            }}>
                <Box sx={{
                    p: 4,
                    borderBottom: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                    background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.05)} 0%, ${alpha(beeColors.secondary.main, 0.05)} 100%)`
                }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                        <Box sx={{
                            width: 40,
                            height: 40,
                            borderRadius: '10px',
                            background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <LessonIcon sx={{ color: 'white', fontSize: 20 }} />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                            <Typography
                                variant="h5"
                                sx={{
                                    fontWeight: 600,
                                    color: beeColors.neutral.main,
                                    mb: 0.5
                                }}
                            >
                                Nội dung khóa học
                            </Typography>
                            <Typography
                                variant="body2"
                                sx={{ color: beeColors.neutral.light }}
                            >
                                Quản lý và sắp xếp nội dung khóa học của bạn
                            </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Chip
                                label={`${content.length} nội dung`}
                                sx={{
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    color: beeColors.primary.main,
                                    fontWeight: 600,
                                    '& .MuiChip-label': {
                                        px: 2
                                    }
                                }}
                            />
                            {reloadingContent && (
                                <CircularProgress size={20} sx={{ color: beeColors.primary.main }} />
                            )}
                        </Box>
                    </Box>
                </Box>

                <Box sx={{ p: 4 }}>

                    {content.length === 0 ? (
                        <Box sx={{
                            textAlign: 'center',
                            py: 8,
                            px: 4
                        }}>
                            <Box sx={{
                                width: 80,
                                height: 80,
                                borderRadius: '20px',
                                background: `linear-gradient(135deg, ${alpha(beeColors.neutral.main, 0.1)} 0%, ${alpha(beeColors.neutral.main, 0.05)} 100%)`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mx: 'auto',
                                mb: 3
                            }}>
                                <LessonIcon sx={{ fontSize: 40, color: beeColors.neutral.light }} />
                            </Box>
                            <Typography variant="h6" sx={{ color: beeColors.neutral.main, mb: 1, fontWeight: 600 }}>
                                Chưa có nội dung nào
                            </Typography>
                            <Typography variant="body2" sx={{ color: beeColors.neutral.light, mb: 4, maxWidth: 400, mx: 'auto' }}>
                                Hãy thêm bài học, bài kiểm tra hoặc bài tập TurboWarp để bắt đầu xây dựng khóa học của bạn
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                                <CustomButton
                                    variant="contained"
                                    startIcon={<LessonIcon />}
                                    onClick={() => handleAddContent('lesson')}
                                    sx={{ minWidth: 140 }}
                                >
                                    Thêm bài học
                                </CustomButton>
                                <CustomButton
                                    variant="outlined"
                                    startIcon={<QuizIcon />}
                                    onClick={() => handleAddContent('quiz')}
                                    sx={{ minWidth: 140 }}
                                >
                                    Thêm bài kiểm tra
                                </CustomButton>
                            </Box>
                        </Box>
                    ) : (
                        <List sx={{
                            position: 'relative',
                            '& .MuiListItem-root': {
                                marginBottom: 1,
                                borderRadius: '8px',
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.neutral.main, 0.02)
                                }
                            }
                        }}>
                            {content
                                .sort((a, b) => a.order - b.order)
                                .map((item, index) => {
                                    return (
                                        <React.Fragment key={index}>
                                            <ListItem
                                                sx={{
                                                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                                                    transform: movingItems.has(index) ? 'scale(1.02)' : 'scale(1)',
                                                    backgroundColor: movingItems.has(index)
                                                        ? alpha(beeColors.primary.main, 0.08)
                                                        : 'transparent',
                                                    borderRadius: movingItems.has(index) ? '12px' : '0px',
                                                    boxShadow: movingItems.has(index)
                                                        ? `0 4px 20px ${alpha(beeColors.primary.main, 0.15)}`
                                                        : 'none',
                                                    position: 'relative',
                                                    // zIndex: movingItems.has(index) ? 10 : 1,
                                                    '&::before': movingItems.has(index) ? {
                                                        content: '""',
                                                        position: 'absolute',
                                                        top: 0,
                                                        left: 0,
                                                        right: 0,
                                                        bottom: 0,
                                                        background: `linear-gradient(45deg, ${alpha(beeColors.primary.main, 0.1)}, ${alpha(beeColors.primary.light, 0.1)})`,
                                                        borderRadius: '12px',
                                                        zIndex: -1,
                                                        animation: movingItems.has(index) ? 'pulse 0.6s ease-in-out' : 'none'
                                                    } : {},
                                                    '@keyframes pulse': {
                                                        '0%': { opacity: 0.5 },
                                                        '50%': { opacity: 1 },
                                                        '100%': { opacity: 0.5 }
                                                    }
                                                }}
                                            >
                                                <ListItemIcon>
                                                    <DragIcon />
                                                </ListItemIcon>
                                                <ListItemIcon>
                                                    {getContentIcon(item)}
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={
                                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                            <Typography variant="body1">
                                                                {item.order}. {item.title}
                                                            </Typography>
                                                            <Chip
                                                                label={getContentTypeLabel(item)}
                                                                size="small"
                                                                color={
                                                                    item._type === 'quiz' ? 'error' :
                                                                        item._type === 'assignment' ? 'secondary' : 'success'
                                                                }
                                                                variant="outlined"
                                                            />
                                                            {item.type === 'lesson' && getContentTypeIcon(item.contentType)}
                                                            {!item.isPublished && (
                                                                <Chip label="Bản nháp" size="small" color="warning" />
                                                            )}
                                                        </Box>
                                                    }
                                                    secondary={
                                                        <Box>
                                                            <Typography variant="body2" color="textSecondary">
                                                                {item.description}
                                                            </Typography>
                                                            {item.duration && (
                                                                <Typography variant="caption" color="textSecondary">
                                                                    Thời lượng: {item.duration} phút
                                                                </Typography>
                                                            )}
                                                        </Box>
                                                    }
                                                />
                                                <ListItemSecondaryAction>
                                                    <Tooltip title="Di chuyển lên">
                                                        <IconButton
                                                            size="small"
                                                            onClick={() => handleMoveContent(index, 'up')}
                                                            disabled={index === 0 || movingItems.size > 0}
                                                            sx={{
                                                                zindex: 100,
                                                                color: index === 0 ? alpha(beeColors.neutral.light, 0.5) : beeColors.primary.main,
                                                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                transform: movingItems.has(index) ? 'translateY(-2px)' : 'translateY(0)',
                                                                '&:hover': {
                                                                    backgroundColor: index === 0 ? 'transparent' : alpha(beeColors.primary.main, 0.1),
                                                                    transform: index === 0 ? 'translateY(0)' : 'translateY(-2px) scale(1.1)',
                                                                    color: index === 0 ? alpha(beeColors.neutral.light, 0.5) : beeColors.primary.dark
                                                                },
                                                                '&:active': {
                                                                    transform: index === 0 ? 'translateY(0)' : 'translateY(-1px) scale(0.95)'
                                                                },
                                                                '&:disabled': {
                                                                    color: alpha(beeColors.neutral.light, 0.3)
                                                                }
                                                            }}
                                                        >
                                                            <UpIcon sx={{
                                                                fontSize: 20,
                                                                filter: movingItems.has(index) ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' : 'none'
                                                            }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="Di chuyển xuống">
                                                        <IconButton
                                                            size="small"
                                                            onClick={() => handleMoveContent(index, 'down')}
                                                            disabled={index === content.length - 1 || movingItems.size > 0}
                                                            sx={{
                                                                color: index === content.length - 1 ? alpha(beeColors.neutral.light, 0.5) : beeColors.primary.main,
                                                                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                                                transform: movingItems.has(index) ? 'translateY(2px)' : 'translateY(0)',
                                                                '&:hover': {
                                                                    backgroundColor: index === content.length - 1 ? 'transparent' : alpha(beeColors.primary.main, 0.1),
                                                                    transform: index === content.length - 1 ? 'translateY(0)' : 'translateY(2px) scale(1.1)',
                                                                    color: index === content.length - 1 ? alpha(beeColors.neutral.light, 0.5) : beeColors.primary.dark
                                                                },
                                                                '&:active': {
                                                                    transform: index === content.length - 1 ? 'translateY(0)' : 'translateY(1px) scale(0.95)'
                                                                },
                                                                '&:disabled': {
                                                                    color: alpha(beeColors.neutral.light, 0.3)
                                                                }
                                                            }}
                                                        >
                                                            <DownIcon sx={{
                                                                fontSize: 20,
                                                                filter: movingItems.has(index) ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' : 'none'
                                                            }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="Chỉnh sửa">
                                                        <IconButton
                                                            size="small"
                                                            onClick={() => {
                                                                handleEditContent(item);
                                                            }}
                                                            color="primary"
                                                            disabled={movingItems.size > 0}
                                                        >
                                                            <EditIcon />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="Xóa">
                                                        <IconButton
                                                            size="small"
                                                            color="error"
                                                            onClick={() => {
                                                                handleDeleteContent(item.id);
                                                            }}
                                                            disabled={movingItems.size > 0}
                                                        >
                                                            <DeleteIcon />
                                                        </IconButton>
                                                    </Tooltip>
                                                </ListItemSecondaryAction>
                                            </ListItem>
                                            {index < content.length - 1 && (
                                                <Divider sx={{
                                                    transition: 'all 0.3s ease',
                                                    opacity: movingItems.has(index) || movingItems.has(index + 1) ? 0.3 : 1,
                                                    transform: movingItems.has(index) || movingItems.has(index + 1) ? 'scaleX(0.8)' : 'scaleX(1)',
                                                    marginY: 0.5
                                                }} />
                                            )}
                                        </React.Fragment>
                                    );
                                })}
                        </List>
                    )}
                </Box>
            </Box>

            {/* Add/Edit Content Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingItem
                        ? `Chỉnh sửa ${dialogType === 'lesson' ? 'bài học' : dialogType === 'quiz' ? 'bài kiểm tra' : 'bài tập TurboWarp'}`
                        : `Thêm ${dialogType === 'lesson' ? 'bài học' : dialogType === 'quiz' ? 'bài kiểm tra' : 'bài tập TurboWarp'} mới`
                    }
                    {dialogType === 'quiz' && selectedQuestions.length > 0 && (
                        <Chip
                            label={`${selectedQuestions.length} câu hỏi đã chọn`}
                            color="primary"
                            size="small"
                            sx={{ ml: 2 }}
                        />
                    )}
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ mt: 2 }}>
                        {renderContentForm()}
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: '10px' }}>Hủy</Button>
                    <Button onClick={handleSaveContent} variant="contained" sx={{ borderRadius: '10px' }}>
                        {editingItem ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default CourseBuilder;
