import React, { useState, useEffect } from 'react';
import { elearningAPI } from '../../../../services';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Avatar,
    Tooltip,
    Alert,
    Snackbar,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Badge
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    PersonAdd as PersonAddIcon,
    Email as EmailIcon,
    Phone as PhoneIcon,
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    TrendingUp as TrendingUpIcon,
    FilterList as FilterIcon
} from '@mui/icons-material';

function StudentManagement({ user }) {
    const [students, setStudents] = useState([]);
    const [filteredStudents, setFilteredStudents] = useState([]);
    const [courses, setCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingStudent, setEditingStudent] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterCourse, setFilterCourse] = useState('');
    const [filterStatus, setFilterStatus] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        courseId: '',
        studentId: '',
        parentName: '',
        parentPhone: '',
        address: '',
        status: 'active'
    });

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            // Load teacher's courses and their enrolled students
            const coursesData = await elearningAPI.teacherAPI.getCourses();
            setCourses(coursesData);

            // Get all students from all courses
            const allStudents = [];
            for (const course of coursesData) {
                if (course.enrolled_count > 0) {
                    // In a real implementation, you'd have an API to get course students
                    // For now, we'll use mock data based on course info
                    const courseStudents = generateMockStudentsForCourse(course);
                    allStudents.push(...courseStudents);
                }
            }

            setStudents(allStudents);
            setFilteredStudents(allStudents);
        } catch (error) {
            console.error('Error loading data:', error);
            showSnackbar('Không thể tải dữ liệu học sinh', 'error');
        } finally {
            setLoading(false);
        }
    };

    const generateMockStudentsForCourse = (course) => {
        // Generate mock students for demonstration
        const mockStudents = [];
        const studentCount = Math.min(course.enrolled_count || 0, 5); // Limit for demo

        for (let i = 1; i <= studentCount; i++) {
            mockStudents.push({
                id: `${course.id}-${i}`,
                firstName: `Học sinh ${i}`,
                lastName: `Khóa ${course.title}`,
                email: `student${i}@course${course.id}.com`,
                phone: `012345678${i}`,
                courseId: course.id,
                courseName: course.title,
                studentId: `HS${course.id}${String(i).padStart(3, '0')}`,
                parentName: `Phụ huynh ${i}`,
                parentPhone: `098765432${i}`,
                address: `Địa chỉ ${i}`,
                status: 'active',
                joinDate: '2024-01-05',
                assignments: Math.floor(Math.random() * 10),
                quizzes: Math.floor(Math.random() * 8),
                avgScore: (Math.random() * 4 + 6).toFixed(1) // 6.0 - 10.0
            });
        }

        return mockStudents;
    };

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };

    // Filter logic
    useEffect(() => {
        let filtered = students;

        if (searchTerm) {
            filtered = filtered.filter(s =>
                `${s.firstName} ${s.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.studentId.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterCourse) {
            filtered = filtered.filter(s => s.courseId === parseInt(filterCourse));
        }

        if (filterStatus) {
            filtered = filtered.filter(s => s.status === filterStatus);
        }

        setFilteredStudents(filtered);
        setPage(0);
    }, [students, searchTerm, filterCourse, filterStatus]);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (student = null) => {
        if (student) {
            setEditingStudent(student);
            setFormData({
                firstName: student.firstName,
                lastName: student.lastName,
                email: student.email,
                phone: student.phone,
                courseId: student.courseId,
                studentId: student.studentId,
                parentName: student.parentName,
                parentPhone: student.parentPhone,
                address: student.address,
                status: student.status
            });
        } else {
            setEditingStudent(null);
            setFormData({
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                courseId: '',
                studentId: '',
                parentName: '',
                parentPhone: '',
                address: '',
                status: 'active'
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingStudent(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveStudent = () => {
        // In a real implementation, this would call an API
        showSnackbar('Tính năng đang được phát triển', 'info');
        handleCloseDialog();
    };

    const handleDeleteStudent = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa học sinh này?')) {
            // In a real implementation, this would call an API
            showSnackbar('Tính năng đang được phát triển', 'info');
        }
    };

    const getStatusChip = (status) => {
        const statusConfig = {
            active: { label: 'Đang học', color: 'success' },
            inactive: { label: 'Tạm nghỉ', color: 'warning' },
            completed: { label: 'Hoàn thành', color: 'info' },
            dropped: { label: 'Bỏ học', color: 'error' }
        };

        const config = statusConfig[status] || statusConfig.active;
        return <Chip label={config.label} color={config.color} size="small" />;
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
                <Typography>Đang tải dữ liệu...</Typography>
            </Box>
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Quản lý học sinh
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<PersonAddIcon />}
                    onClick={() => handleOpenDialog()}
                    sx={{ borderRadius: '10px' }}
                >
                    Thêm học sinh
                </Button>
            </Box>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng học sinh
                            </Typography>
                            <Typography variant="h4" color="primary">
                                {students.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đang học
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {students.filter(s => s.status === 'active').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Khóa học
                            </Typography>
                            <Typography variant="h4" color="info.main">
                                {courses.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm TB
                            </Typography>
                            <Typography variant="h4" color="warning.main">
                                {students.length > 0 ?
                                    (students.reduce((sum, s) => sum + parseFloat(s.avgScore), 0) / students.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: '10px' }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <TextField
                            fullWidth
                            size="small"
                            placeholder="Tìm kiếm học sinh..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Khóa học</InputLabel>
                            <Select
                                value={filterCourse}
                                onChange={(e) => setFilterCourse(e.target.value)}
                                label="Khóa học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {courses.map(course => (
                                    <MenuItem key={course.id} value={course.id}>{course.title}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Trạng thái</InputLabel>
                            <Select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                label="Trạng thái"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="active">Đang học</MenuItem>
                                <MenuItem value="inactive">Tạm nghỉ</MenuItem>
                                <MenuItem value="completed">Hoàn thành</MenuItem>
                                <MenuItem value="dropped">Bỏ học</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                </Grid>
            </Paper>

            {/* Students Table */}
            <Paper sx={{ borderRadius: '10px' }}>
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Học sinh</TableCell>
                                <TableCell>Khóa học</TableCell>
                                <TableCell>Liên hệ</TableCell>
                                <TableCell>Trạng thái</TableCell>
                                <TableCell>Thống kê</TableCell>
                                <TableCell align="center">Thao tác</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {filteredStudents
                                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                .map((student) => (
                                    <TableRow key={student.id} hover>
                                        <TableCell>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                                                    {student.firstName.charAt(0)}
                                                </Avatar>
                                                <Box>
                                                    <Typography variant="subtitle2">
                                                        {student.firstName} {student.lastName}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        ID: {student.studentId}
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            <Typography variant="body2">
                                                {student.courseName}
                                            </Typography>
                                        </TableCell>
                                        <TableCell>
                                            <Box>
                                                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                                    <EmailIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                    {student.email}
                                                </Typography>
                                                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <PhoneIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                    {student.phone}
                                                </Typography>
                                            </Box>
                                        </TableCell>
                                        <TableCell>
                                            {getStatusChip(student.status)}
                                        </TableCell>
                                        <TableCell>
                                            <Box>
                                                <Typography variant="body2" sx={{ mb: 0.5 }}>
                                                    <AssignmentIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                    {student.assignments} bài tập
                                                </Typography>
                                                <Typography variant="body2" sx={{ mb: 0.5 }}>
                                                    <QuizIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                    {student.quizzes} bài kiểm tra
                                                </Typography>
                                                <Typography variant="body2">
                                                    <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                    Điểm TB: {student.avgScore}
                                                </Typography>
                                            </Box>
                                        </TableCell>
                                        <TableCell align="center">
                                            <Tooltip title="Chỉnh sửa">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleOpenDialog(student)}
                                                >
                                                    <EditIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Xóa">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleDeleteStudent(student.id)}
                                                    color="error"
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </TableCell>
                                    </TableRow>
                                ))}
                        </TableBody>
                    </Table>
                </TableContainer>
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    component="div"
                    count={filteredStudents.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            </Paper>

            {/* Add/Edit Student Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingStudent ? 'Chỉnh sửa học sinh' : 'Thêm học sinh mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Họ *"
                                value={formData.firstName}
                                onChange={(e) => handleFormChange('firstName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tên *"
                                value={formData.lastName}
                                onChange={(e) => handleFormChange('lastName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Email *"
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleFormChange('email', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Số điện thoại"
                                value={formData.phone}
                                onChange={(e) => handleFormChange('phone', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Khóa học *</InputLabel>
                                <Select
                                    value={formData.courseId}
                                    onChange={(e) => handleFormChange('courseId', e.target.value)}
                                    label="Khóa học *"
                                >
                                    {courses.map(course => (
                                        <MenuItem key={course.id} value={course.id}>{course.title}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Mã học sinh"
                                value={formData.studentId}
                                onChange={(e) => handleFormChange('studentId', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tên phụ huynh"
                                value={formData.parentName}
                                onChange={(e) => handleFormChange('parentName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="SĐT phụ huynh"
                                value={formData.parentPhone}
                                onChange={(e) => handleFormChange('parentPhone', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Địa chỉ"
                                multiline
                                rows={2}
                                value={formData.address}
                                onChange={(e) => handleFormChange('address', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <FormControl fullWidth>
                                <InputLabel>Trạng thái</InputLabel>
                                <Select
                                    value={formData.status}
                                    onChange={(e) => handleFormChange('status', e.target.value)}
                                    label="Trạng thái"
                                >
                                    <MenuItem value="active">Đang học</MenuItem>
                                    <MenuItem value="inactive">Tạm nghỉ</MenuItem>
                                    <MenuItem value="completed">Hoàn thành</MenuItem>
                                    <MenuItem value="dropped">Bỏ học</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Hủy</Button>
                    <Button onClick={handleSaveStudent} variant="contained">
                        {editingStudent ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default StudentManagement;
