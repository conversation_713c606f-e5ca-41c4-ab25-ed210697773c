import { useState } from 'react';
import { useNavigate, Routes, Route, useLocation, useParams } from 'react-router-dom';
import {
    Box,
    Drawer,
    AppBar,
    Toolbar,
    List,
    Typography,
    Divider,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    IconButton,
    Badge,
    Avatar,
    Menu,
    MenuItem,
    useMediaQuery,
    useTheme,
    alpha,
    Tooltip
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    QuestionAnswer as QuestionIcon,
    School as ClassIcon,
    People as StudentsIcon,
    Notifications as NotificationIcon,
    AccountCircle as AccountIcon,
    Settings as SettingsIcon,
    Logout as LogoutIcon,
    Home as HomeIcon,
    Menu as MenuIcon,
    Close as CloseIcon
} from '@mui/icons-material';
import { beeColors } from '../../Common/CustomButton';
import { TeacherLayoutProvider, useTeacherLayout } from './contexts/TeacherLayoutContext';

// Import các component con (sẽ tạo sau)
import TeacherDashboard from './components/TeacherDashboard';
import CourseManagement from './components/CourseManagement';
import QuestionBank from './components/QuestionBank';
import StudentManagement from './components/StudentManagement';
import CourseBuilder from './components/CourseBuilder';
import CoursePreviewPage from './components/CoursePreviewPage';

const drawerWidth = 280;
const collapsedDrawerWidth = 80;

// Wrapper components to handle params
function CourseBuilderWrapper() {
    const { courseId } = useParams();
    const navigate = useNavigate();

    return (
        <CourseBuilder
            courseId={courseId}
            onClose={() => navigate('/teacher/courses')}
        />
    );
}

function CoursePreviewWrapper() {
    const { courseId } = useParams();
    const navigate = useNavigate();

    return (
        <CoursePreviewPage
            courseId={courseId}
            onClose={() => navigate(`/teacher/courses/${courseId}/builder`)}
        />
    );
}

const menuItems = [
    {
        id: '',
        label: 'Tổng quan',
        icon: <DashboardIcon />,
        path: '/teacher',
        has_divider: false,
        color: beeColors.primary.main
    },
    {
        id: 'courses',
        label: 'Quản lý khóa học',
        icon: <ClassIcon />,
        path: '/teacher/courses',
        has_divider: false,
        color: beeColors.secondary.main
    },
    {
        id: 'questions',
        label: 'Ngân hàng câu hỏi',
        icon: <QuestionIcon />,
        path: '/teacher/questions',
        has_divider: false,
        color: beeColors.accent.main
    },
    {
        id: 'students',
        label: 'Quản lý học sinh',
        icon: <StudentsIcon />,
        path: '/teacher/students',
        has_divider: false,
        color: beeColors.primary.main
    },
    {
        id: 'home',
        label: 'Trang chủ',
        icon: <HomeIcon />,
        path: '/',
        has_divider: true,
        color: beeColors.neutral.main
    }
];

function TeacherLayout({ user }) {
    const navigate = useNavigate();
    const location = useLocation();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const { isFullScreen } = useTeacherLayout();

    // Calculate current drawer width

    const [anchorEl, setAnchorEl] = useState(null);
    const [mobileOpen, setMobileOpen] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
    const currentDrawerWidth = sidebarCollapsed ? collapsedDrawerWidth : drawerWidth;

    const handleMenuClick = (path) => {
        navigate(path);
    };

    const getCurrentPath = () => {
        const path = location.pathname;
        if (path === '/teacher') return '';
        return path.replace('/teacher/', '');
    };

    const handleProfileMenuOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleProfileMenuClose = () => {
        setAnchorEl(null);
    };

    const handleDrawerToggle = () => {
        if (isMobile) {
            setMobileOpen(!mobileOpen);
        } else {
            setSidebarCollapsed(!sidebarCollapsed);
        }
    };

    const renderDrawerContent = () => (
        <Box>
            <Toolbar sx={{ minHeight: '70px !important' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <IconButton
                        onClick={handleDrawerToggle}
                        sx={{
                            // width: '100%',
                            borderRadius: '12px',
                            p: 1.5,
                            justifyContent: 'flex-start',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                backgroundColor: alpha(beeColors.primary.main, 0.1),
                                transform: 'scale(1.02)'
                            }
                        }}
                    >
                        <MenuIcon
                            sx={{
                                color: beeColors.neutral.light,
                                fontSize: '1.2rem',
                                transition: 'transform 0.3s ease',
                                transform: sidebarCollapsed ? 'rotate(180deg)' : 'rotate(0deg)'
                            }}
                        />
                    </IconButton>
                </Box>
            </Toolbar>
            <Divider sx={{ borderColor: alpha(beeColors.neutral.main, 0.1) }} />
            <List sx={{ px: 2, py: 1 }}>
                {menuItems.map((item) => (
                    <Box key={item.id}>
                        {item.has_divider && (
                            <Divider sx={{
                                my: 2,
                                borderColor: alpha(beeColors.neutral.main, 0.1)
                            }} />
                        )}
                        <ListItem disablePadding sx={{ mb: 0.5 }}>
                            <Tooltip
                                title={sidebarCollapsed ? item.label : ''}
                                placement="right"
                                arrow
                            >
                                <ListItemButton
                                    selected={getCurrentPath() === item.id}
                                    onClick={() => handleMenuClick(item.path)}
                                    sx={{
                                        borderRadius: '12px',
                                        py: 1.5,
                                        px: sidebarCollapsed ? 1 : 2,
                                        justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                        width: '100%',
                                        '&.Mui-selected': {
                                            backgroundColor: alpha(beeColors.primary.main, 0.15),
                                            color: beeColors.primary.main,
                                            '&:hover': {
                                                backgroundColor: alpha(beeColors.primary.main, 0.2),
                                            },
                                            '&::before': {
                                                content: '""',
                                                position: 'absolute',
                                                left: 0,
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                width: '4px',
                                                height: '60%',
                                                backgroundColor: beeColors.primary.main,
                                                borderRadius: '0 4px 4px 0'
                                            }
                                        },
                                        '&:hover': {
                                            backgroundColor: alpha(beeColors.neutral.main, 0.08),
                                            transform: 'translateX(4px)'
                                        },
                                    }}
                                >
                                    <ListItemIcon
                                        sx={{
                                            color: getCurrentPath() === item.id
                                                ? beeColors.primary.main
                                                : item.color,
                                            minWidth: sidebarCollapsed ? 'auto' : 40,
                                            justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                                            transition: 'color 0.3s ease'
                                        }}
                                    >
                                        {item.icon}
                                    </ListItemIcon>
                                    {!sidebarCollapsed && (
                                        <ListItemText
                                            primary={item.label}
                                            sx={{
                                                '& .MuiListItemText-primary': {
                                                    fontWeight: getCurrentPath() === item.id ? 600 : 500,
                                                    fontSize: '0.9rem',
                                                    color: getCurrentPath() === item.id
                                                        ? beeColors.primary.main
                                                        : beeColors.neutral.main
                                                }
                                            }}
                                        />
                                    )}
                                </ListItemButton>
                            </Tooltip>
                        </ListItem>
                    </Box>
                ))}
            </List>
        </Box>
    );

    const renderContent = () => {
        return (
            <Routes>
                <Route path="/" element={<TeacherDashboard user={user} />} />
                <Route path="/courses" element={<CourseManagement user={user} />} />
                <Route path="/courses/:courseId/builder" element={<CourseBuilderWrapper />} />
                <Route path="/courses/:courseId/preview" element={<CoursePreviewWrapper />} />
                <Route path="/questions" element={<QuestionBank user={user} />} />
                <Route path="/students" element={<StudentManagement user={user} />} />
            </Routes>
        );
    };

    return (
        <Box sx={{ display: 'flex' }}>
            {/* App Bar - Hidden in full screen mode */}
            {!isFullScreen && (
                <AppBar
                    position="fixed"
                    sx={{
                        width: { sm: `calc(100% - ${currentDrawerWidth}px)` },
                        ml: { sm: `${currentDrawerWidth}px` },
                        backgroundColor: beeColors.background.paper,
                        color: beeColors.neutral.main,
                        boxShadow: `0 2px 12px ${alpha(beeColors.neutral.main, 0.1)}`,
                        borderBottom: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                        transition: 'width 0.3s ease, margin-left 0.3s ease',
                    }}
                >
                    <Toolbar sx={{ minHeight: '70px !important' }}>
                        <IconButton
                            color="inherit"
                            aria-label="open drawer"
                            edge="start"
                            onClick={handleDrawerToggle}
                            sx={{ mr: 2, display: { sm: 'none' } }}
                        >
                            <MenuIcon />
                        </IconButton>

                        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                            <Box
                                sx={{
                                    width: 40,
                                    height: 40,
                                    borderRadius: '40px',
                                    // background: beeColors.background.gradient,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mr: 2,
                                    p: 0.5,
                                    border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                    pointer: 'cursor'
                                }}
                                onClick={() => navigate('/teacher')}
                            >
                                <img
                                    src="/beeIco.svg"
                                    alt="BeE STEM Logo"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'contain'
                                    }}
                                />
                            </Box>
                            <Box>
                                <Typography
                                    variant="h6"
                                    component="div"
                                    sx={{
                                        fontWeight: 600,
                                        color: beeColors.neutral.main,
                                        fontSize: '1.1rem'
                                    }}
                                >
                                    BeE STEM Teacher
                                </Typography>
                                <Typography
                                    variant="caption"
                                    sx={{
                                        color: beeColors.neutral.light,
                                        fontSize: '0.75rem'
                                    }}
                                >
                                    Chào mừng, {user?.first_name || 'Giáo viên'}!
                                </Typography>
                            </Box>
                        </Box>

                        {/* Notifications */}
                        <IconButton
                            sx={{
                                mr: 1,
                                color: beeColors.neutral.light,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    color: beeColors.primary.main
                                }
                            }}
                        >
                            <Badge
                                badgeContent={4}
                                sx={{
                                    '& .MuiBadge-badge': {
                                        backgroundColor: beeColors.primary.main,
                                        color: 'white'
                                    }
                                }}
                            >
                                <NotificationIcon />
                            </Badge>
                        </IconButton>

                        {/* Profile Menu */}
                        <IconButton
                            onClick={handleProfileMenuOpen}
                            sx={{
                                ml: 1,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.primary.main, 0.1)
                                }
                            }}
                        >
                            <Avatar
                                sx={{
                                    width: 36,
                                    height: 36,
                                    background: beeColors.background.gradient,
                                    fontWeight: 600
                                }}
                                src={user?.avatar_url}
                            >
                            </Avatar>
                        </IconButton>

                        <Menu
                            anchorEl={anchorEl}
                            open={Boolean(anchorEl)}
                            onClose={handleProfileMenuClose}
                            anchorOrigin={{
                                vertical: 'bottom',
                                horizontal: 'right',
                            }}
                            transformOrigin={{
                                vertical: 'top',
                                horizontal: 'right',
                            }}
                            PaperProps={{
                                sx: {
                                    borderRadius: '12px',
                                    boxShadow: `0 8px 32px ${alpha(beeColors.neutral.main, 0.15)}`,
                                    border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                    mt: 1
                                }
                            }}
                        >
                            <MenuItem
                                onClick={handleProfileMenuClose}
                                sx={{
                                    borderRadius: '8px',
                                    mx: 1,
                                    my: 0.5,
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.primary.main, 0.1)
                                    }
                                }}
                            >
                                <ListItemIcon sx={{ color: beeColors.primary.main }}>
                                    <AccountIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText>Hồ sơ cá nhân</ListItemText>
                            </MenuItem>
                            <MenuItem
                                onClick={handleProfileMenuClose}
                                sx={{
                                    borderRadius: '8px',
                                    mx: 1,
                                    my: 0.5,
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.secondary.main, 0.1)
                                    }
                                }}
                            >
                                <ListItemIcon sx={{ color: beeColors.secondary.main }}>
                                    <SettingsIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText>Cài đặt</ListItemText>
                            </MenuItem>
                            <Divider sx={{ my: 1 }} />
                            <MenuItem
                                onClick={handleProfileMenuClose}
                                sx={{
                                    borderRadius: '8px',
                                    mx: 1,
                                    my: 0.5,
                                    '&:hover': {
                                        backgroundColor: alpha('#E74C3C', 0.1)
                                    }
                                }}
                            >
                                <ListItemIcon sx={{ color: '#E74C3C' }}>
                                    <LogoutIcon fontSize="small" />
                                </ListItemIcon>
                                <ListItemText>Đăng xuất</ListItemText>
                            </MenuItem>
                        </Menu>
                    </Toolbar>
                </AppBar>
            )}

            {/* Sidebar - Hidden in full screen mode */}
            {!isFullScreen && (
                <Box
                    component="nav"
                    sx={{
                        width: { sm: currentDrawerWidth },
                        flexShrink: { sm: 0 },
                        transition: 'width 0.3s ease'
                    }}
                >
                    {/* Mobile drawer */}
                    <Drawer
                        variant="temporary"
                        open={mobileOpen}
                        onClose={handleDrawerToggle}
                        ModalProps={{
                            keepMounted: true, // Better open performance on mobile.
                        }}
                        sx={{
                            display: { xs: 'block', sm: 'none' },
                            '& .MuiDrawer-paper': {
                                boxSizing: 'border-box',
                                width: drawerWidth,
                                backgroundColor: beeColors.background.paper,
                                borderRight: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                            },
                        }}
                    >
                        {renderDrawerContent()}
                    </Drawer>

                    {/* Desktop drawer */}
                    <Drawer
                        variant="permanent"
                        sx={{
                            display: { xs: 'none', sm: 'block' },
                            '& .MuiDrawer-paper': {
                                boxSizing: 'border-box',
                                width: currentDrawerWidth,
                                backgroundColor: beeColors.background.paper,
                                borderRight: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                transition: 'width 0.3s ease',
                                overflow: 'hidden'
                            },
                        }}
                        open
                    >
                        {renderDrawerContent()}
                    </Drawer>
                </Box>
            )}

            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    backgroundColor: isFullScreen ? '#fff' : beeColors.background.main,
                    p: isFullScreen ? 0 : { xs: 2, sm: 3 },
                    minHeight: '100vh',
                    width: isFullScreen ? '100%' : { sm: `calc(100% - ${currentDrawerWidth}px)` },
                    transition: 'width 0.3s ease',
                    position: 'relative',
                    ...(!isFullScreen && {
                        '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            height: '200px',
                            background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.05)} 0%, ${alpha(beeColors.secondary.main, 0.05)} 100%)`,
                            zIndex: 0,
                            pointerEvents: 'none'
                        }
                    })
                }}
            >
                {!isFullScreen && <Toolbar sx={{ minHeight: '70px !important' }} />}
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                    {renderContent()}
                </Box>
            </Box>
        </Box>
    );
}

// Main Teacher component with context provider
function Teacher({ user }) {
    return (
        <TeacherLayoutProvider>
            <TeacherLayout user={user} />
        </TeacherLayoutProvider>
    );
}

export default Teacher;
