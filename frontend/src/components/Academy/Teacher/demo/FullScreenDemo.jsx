import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { useTeacherLayout } from '../contexts/TeacherLayoutContext';
import CustomButton, { beeColors } from '../../../Common/CustomButton';

function FullScreenDemo() {
    const { isFullScreen, enterFullScreen, exitFullScreen } = useTeacherLayout();

    const handleToggleFullScreen = () => {
        if (isFullScreen) {
            exitFullScreen();
        } else {
            enterFullScreen('Demo Full Screen Mode');
        }
    };

    return (
        <Box sx={{ p: 3 }}>
            <Paper 
                sx={{ 
                    p: 4, 
                    borderRadius: '16px',
                    backgroundColor: beeColors.background.paper,
                    textAlign: 'center'
                }}
            >
                <Typography variant="h4" sx={{ mb: 3, color: beeColors.neutral.main }}>
                    Full Screen Mode Demo
                </Typography>
                
                <Typography variant="body1" sx={{ mb: 3, color: beeColors.neutral.light }}>
                    Current mode: {isFullScreen ? 'Full Screen' : 'Normal'}
                </Typography>

                <CustomButton
                    variant="contained"
                    onClick={handleToggleFullScreen}
                    sx={{ px: 4, py: 1.5 }}
                >
                    {isFullScreen ? 'Exit Full Screen' : 'Enter Full Screen'}
                </CustomButton>

                <Box sx={{ mt: 4, p: 3, backgroundColor: beeColors.background.main, borderRadius: '12px' }}>
                    <Typography variant="h6" sx={{ mb: 2, color: beeColors.neutral.main }}>
                        Instructions:
                    </Typography>
                    <Typography variant="body2" sx={{ color: beeColors.neutral.light, lineHeight: 1.6 }}>
                        • Click "Enter Full Screen" to hide the sidebar and expand content to full width<br/>
                        • Click "Exit Full Screen" to restore normal layout with sidebar<br/>
                        • This simulates how CourseBuilder and CoursePreviewPage work
                    </Typography>
                </Box>
            </Paper>
        </Box>
    );
}

export default FullScreenDemo;
