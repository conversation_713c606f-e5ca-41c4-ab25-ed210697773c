import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Grid,
  Chip,
  Divider
} from '@mui/material';
import { elearningAPI } from '../../services';

const TestELearningAPI = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});
  const [error, setError] = useState(null);

  const testAPI = async (apiName, apiCall) => {
    setLoading(true);
    setError(null);
    try {
      const result = await apiCall();
      setResults(prev => ({
        ...prev,
        [apiName]: {
          success: true,
          data: result,
          timestamp: new Date().toLocaleTimeString()
        }
      }));
    } catch (err) {
      setResults(prev => ({
        ...prev,
        [apiName]: {
          success: false,
          error: err.message,
          timestamp: new Date().toLocaleTimeString()
        }
      }));
      setError(`${apiName} failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testAPIs = [
    {
      name: 'Get Subjects',
      call: () => elearningAPI.getSubjects(),
      description: 'Lấy danh sách môn học'
    },
    {
      name: 'Get Grades',
      call: () => elearningAPI.getGrades(),
      description: 'Lấy danh sách khối lớp'
    },
    {
      name: 'Teacher Dashboard',
      call: () => elearningAPI.teacherAPI.getDashboard(),
      description: 'Dashboard thống kê cho giáo viên'
    },
    {
      name: 'Teacher Courses',
      call: () => elearningAPI.teacherAPI.getCourses(),
      description: 'Danh sách khóa học của giáo viên'
    },
    {
      name: 'Student Dashboard',
      call: () => elearningAPI.studentAPI.getDashboard(),
      description: 'Dashboard thống kê cho học sinh'
    },
    {
      name: 'Available Courses',
      call: () => elearningAPI.studentAPI.getAvailableCourses(),
      description: 'Khóa học có sẵn cho học sinh'
    },
    {
      name: 'Enrolled Courses',
      call: () => elearningAPI.studentAPI.getEnrolledCourses(),
      description: 'Khóa học đã đăng ký'
    }
  ];

  const renderResult = (apiName) => {
    const result = results[apiName];
    if (!result) return null;

    return (
      <Box sx={{ mt: 2 }}>
        <Chip 
          label={result.success ? 'Success' : 'Error'} 
          color={result.success ? 'success' : 'error'}
          size="small"
        />
        <Typography variant="caption" sx={{ ml: 1 }}>
          {result.timestamp}
        </Typography>
        
        {result.success ? (
          <Box sx={{ mt: 1, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem', overflow: 'auto' }}>
              {JSON.stringify(result.data, null, 2)}
            </Typography>
          </Box>
        ) : (
          <Alert severity="error" sx={{ mt: 1 }}>
            {result.error}
          </Alert>
        )}
      </Box>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        E-Learning API Test
      </Typography>
      
      <Typography variant="body1" color="textSecondary" gutterBottom>
        Test các API endpoints của hệ thống E-Learning
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {testAPIs.map((api, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {api.name}
                </Typography>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {api.description}
                </Typography>
                
                <Button
                  variant="contained"
                  onClick={() => testAPI(api.name, api.call)}
                  disabled={loading}
                  sx={{ mt: 2 }}
                >
                  {loading ? <CircularProgress size={20} /> : 'Test API'}
                </Button>

                {renderResult(api.name)}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Divider sx={{ my: 4 }} />

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Test All APIs
          </Typography>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Chạy tất cả API tests cùng lúc
          </Typography>
          
          <Button
            variant="outlined"
            onClick={async () => {
              for (const api of testAPIs) {
                await testAPI(api.name, api.call);
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }}
            disabled={loading}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={20} /> : 'Test All'}
          </Button>
        </CardContent>
      </Card>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          API Endpoints:
        </Typography>
        <Box component="ul" sx={{ pl: 2 }}>
          <li>/api/elearning/subjects/ - Danh sách môn học</li>
          <li>/api/elearning/grades/ - Danh sách khối lớp</li>
          <li>/api/elearning/teacher/dashboard/ - Dashboard giáo viên</li>
          <li>/api/elearning/teacher/courses/ - Khóa học của giáo viên</li>
          <li>/api/elearning/student/dashboard/ - Dashboard học sinh</li>
          <li>/api/elearning/student/courses/available/ - Khóa học có sẵn</li>
          <li>/api/elearning/student/courses/enrolled/ - Khóa học đã đăng ký</li>
        </Box>
      </Box>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Hướng dẫn:
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <li>Đảm bảo backend Django đang chạy</li>
          <li>Đăng nhập với tài khoản teacher hoặc student</li>
          <li>Chạy migrations: <code>python manage.py migrate</code></li>
          <li>Tạo dữ liệu mẫu: <code>python manage.py create_elearning_sample_data</code></li>
          <li>Test từng API hoặc test tất cả cùng lúc</li>
        </Box>
      </Box>
    </Box>
  );
};

export default TestELearningAPI;
