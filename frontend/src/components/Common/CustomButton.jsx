import { Button, styled } from "@mui/material";

// BeE STEM Solutions Color Palette
const beeColors = {
    primary: {
        main: '#FF6B35', // Vibrant Orange - representing energy and creativity
        light: '#FF8A5B',
        dark: '#E55A2B',
        contrastText: '#FFFFFF'
    },
    secondary: {
        main: '#4ECDC4', // Teal - representing growth and innovation
        light: '#7EDDD6',
        dark: '#3BA99F',
        contrastText: '#FFFFFF'
    },
    accent: {
        main: '#FFE66D', // Yellow - representing brightness and learning
        light: '#FFED8A',
        dark: '#E6CF5C',
        contrastText: '#2C3E50'
    },
    neutral: {
        main: '#2C3E50', // Dark Blue-Gray - representing professionalism
        light: '#34495E',
        dark: '#1A252F',
        contrastText: '#FFFFFF'
    },
    background: {
        main: '#F8F9FA',
        paper: '#FFFFFF',
        gradient: 'linear-gradient(135deg, #FF6B35 0%, #4ECDC4 100%)'
    }
};

const CustomButton = styled(Button)(({ theme, variant = 'contained', color = 'primary' }) => {
    const getButtonStyles = () => {
        switch (variant) {
            case 'outlined':
                return {
                    backgroundColor: 'transparent',
                    color: beeColors[color]?.main || beeColors.primary.main,
                    border: `2px solid ${beeColors[color]?.main || beeColors.primary.main}`,
                    "&:hover": {
                        backgroundColor: beeColors[color]?.main || beeColors.primary.main,
                        color: beeColors[color]?.contrastText || beeColors.primary.contrastText,
                        transform: 'translateY(-2px)',
                        boxShadow: `0 8px 25px ${beeColors[color]?.main || beeColors.primary.main}40`
                    }
                };
            case 'text':
                return {
                    backgroundColor: 'transparent',
                    color: beeColors[color]?.main || beeColors.primary.main,
                    "&:hover": {
                        backgroundColor: `${beeColors[color]?.main || beeColors.primary.main}10`,
                        transform: 'translateY(-1px)'
                    }
                };
            default: // contained
                return {
                    backgroundColor: beeColors[color]?.main || beeColors.primary.main,
                    color: beeColors[color]?.contrastText || beeColors.primary.contrastText,
                    "&:hover": {
                        backgroundColor: beeColors[color]?.dark || beeColors.primary.dark,
                        transform: 'translateY(-2px)',
                        boxShadow: `0 8px 25px ${beeColors[color]?.main || beeColors.primary.main}40`
                    }
                };
        }
    };

    return {
        borderRadius: "12px",
        textTransform: "none",
        fontWeight: 600,
        fontSize: "0.95rem",
        padding: "10px 24px",
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        ...getButtonStyles(),
        "&:active": {
            transform: 'translateY(0px)',
        },
        "&:disabled": {
            backgroundColor: '#E0E0E0',
            color: '#9E9E9E',
            transform: 'none',
            boxShadow: 'none'
        }
    };
});

export default CustomButton;
export { beeColors };
