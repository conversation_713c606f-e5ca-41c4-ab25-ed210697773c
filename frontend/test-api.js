// Test script to verify elearningAPI functions
import { elearningAPI } from './src/services/index.js';

async function testAPI() {
    console.log('Testing elearningAPI...');
    
    try {
        // Test common functions
        console.log('Testing getSubjects...');
        const subjects = await elearningAPI.getSubjects();
        console.log('✅ getSubjects works:', subjects);
        
        console.log('Testing getGrades...');
        const grades = await elearningAPI.getGrades();
        console.log('✅ getGrades works:', grades);
        
        // Test teacherAPI functions
        console.log('Testing teacherAPI.getSubjects...');
        const teacherSubjects = await elearningAPI.teacherAPI.getSubjects();
        console.log('✅ teacherAPI.getSubjects works:', teacherSubjects);
        
        console.log('Testing teacherAPI.getGrades...');
        const teacherGrades = await elearningAPI.teacherAPI.getGrades();
        console.log('✅ teacherAPI.getGrades works:', teacherGrades);
        
        console.log('All API tests passed! 🎉');
        
    } catch (error) {
        console.error('❌ API test failed:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testAPI();
