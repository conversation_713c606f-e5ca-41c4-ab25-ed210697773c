from django.urls import path
from .views import (
    # Common views
    SubjectListView, GradeListView,

    # Teacher views
    TeacherCourseListView, TeacherCourseDetailView, TeacherCoursePublishView,
    TeacherCourseContentView, TeacherLessonView, TeacherDashboardView,
    TeacherQuizView, TeacherAssignmentView, TeacherQuestionBankView,
    TeacherSubjectListView, TeacherGradeListView,

    # Student views
    StudentAvailableCoursesView, StudentEnrolledCoursesView, StudentCourseDetailView,
    StudentCoursePurchaseView, StudentCourseContentView, StudentDashboardView,
    StudentCourseProgressView, StudentLessonProgressView, StudentCourseCompleteView,
    StudentQuizAttemptView, StudentQuizSubmitView, StudentQuizCompleteView,
    StudentAssignmentSubmitView, StudentAssignmentSubmissionsView,
)

urlpatterns = [
    # ==================== COMMON ENDPOINTS ====================
    path('subjects/', SubjectListView.as_view(), name='elearning-subjects'),
    path('grades/', GradeListView.as_view(), name='elearning-grades'),

    # ==================== TEACHER ENDPOINTS ====================
    # Dashboard
    path('teacher/dashboard/', TeacherDashboardView.as_view(),
         name='teacher-dashboard'),

    # Course management
    path('teacher/courses/', TeacherCourseListView.as_view(), name='teacher-courses'),
    path('teacher/courses/<int:pk>/', TeacherCourseDetailView.as_view(),
         name='teacher-course-detail'),
    path('teacher/courses/<int:pk>/publish/',
         TeacherCoursePublishView.as_view(), name='teacher-course-publish'),

    # Course content management
    path('teacher/courses/<int:pk>/content/',
         TeacherCourseContentView.as_view(), name='teacher-course-content'),

    # Lesson management
    path('teacher/courses/<int:course_pk>/lessons/',
         TeacherLessonView.as_view(), name='teacher-lessons'),
    path('teacher/courses/<int:course_pk>/lessons/<int:lesson_pk>/',
         TeacherLessonView.as_view(), name='teacher-lesson-detail'),

    # Quiz management
    path('teacher/courses/<int:course_pk>/quizzes/',
         TeacherQuizView.as_view(), name='teacher-quizzes'),
    path('teacher/courses/<int:course_pk>/quizzes/<int:quiz_pk>/',
         TeacherQuizView.as_view(), name='teacher-quiz-detail'),

    # Assignment management
    path('teacher/courses/<int:course_pk>/assignments/',
         TeacherAssignmentView.as_view(), name='teacher-assignments'),
    path('teacher/courses/<int:course_pk>/assignments/<int:assignment_pk>/',
         TeacherAssignmentView.as_view(), name='teacher-assignment-detail'),

    # Question bank
    path('teacher/questions/', TeacherQuestionBankView.as_view(),
         name='teacher-questions'),
    path('teacher/questions/<int:question_pk>/',
         TeacherQuestionBankView.as_view(), name='teacher-question-detail'),

    # Teacher-specific subjects and grades
    path('teacher/subjects/', TeacherSubjectListView.as_view(),
         name='teacher-subjects'),
    path('teacher/grades/', TeacherGradeListView.as_view(),
         name='teacher-grades'),

    # ==================== STUDENT ENDPOINTS ====================
    # Dashboard
    path('student/dashboard/', StudentDashboardView.as_view(),
         name='student-dashboard'),

    # Course discovery
    path('student/courses/available/', StudentAvailableCoursesView.as_view(),
         name='student-available-courses'),
    path('student/courses/enrolled/', StudentEnrolledCoursesView.as_view(),
         name='student-enrolled-courses'),
    path('student/courses/<int:pk>/', StudentCourseDetailView.as_view(),
         name='student-course-detail'),

    # Course enrollment
    path('student/courses/<int:pk>/purchase/',
         StudentCoursePurchaseView.as_view(), name='student-course-purchase'),

    # Course content access
    path('student/courses/<int:pk>/content/',
         StudentCourseContentView.as_view(), name='student-course-content'),

    # Progress tracking
    path('student/courses/<int:course_pk>/progress/',
         StudentCourseProgressView.as_view(), name='student-course-progress'),
    path('student/courses/<int:course_pk>/lessons/<int:lesson_pk>/progress/',
         StudentLessonProgressView.as_view(), name='student-lesson-progress'),
    path('student/courses/<int:course_pk>/complete/',
         StudentCourseCompleteView.as_view(), name='student-course-complete'),

    # Quiz attempts
    path('student/courses/<int:course_pk>/quizzes/<int:quiz_pk>/attempt/',
         StudentQuizAttemptView.as_view(), name='student-quiz-attempt'),
    path('student/courses/<int:course_pk>/quizzes/<int:quiz_pk>/attempt/<int:attempt_pk>/submit/',
         StudentQuizSubmitView.as_view(), name='student-quiz-submit'),
    path('student/courses/<int:course_pk>/quizzes/<int:quiz_pk>/complete/',
         StudentQuizCompleteView.as_view(), name='student-quiz-complete'),

    # Assignment submissions
    path('student/courses/<int:course_pk>/assignments/<int:assignment_pk>/submit/',
         StudentAssignmentSubmitView.as_view(), name='student-assignment-submit'),
    path('student/courses/<int:course_pk>/assignments/<int:assignment_pk>/submissions/',
         StudentAssignmentSubmissionsView.as_view(), name='student-assignment-submissions'),
]
