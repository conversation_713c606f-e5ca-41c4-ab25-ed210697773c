from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from elearning_api.models import Subject, Grade, Course, Lesson, Quiz, Question, Assignment
from auth_api.models import Profile


class Command(BaseCommand):
    help = 'Create sample data for e-learning system'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data for e-learning system...')

        # Create subjects
        subjects_data = [
            {'name': 'Scratch Programming', 'description': 'Learn programming with Scratch',
                'color': '#FF6B35', 'icon': 'code'},
            {'name': 'TurboWarp', 'description': 'Advanced Scratch with TurboWarp',
                'color': '#4ECDC4', 'icon': 'rocket'},
            {'name': 'Game Development', 'description': 'Create games with visual programming',
                'color': '#45B7D1', 'icon': 'gamepad'},
            {'name': 'Animation', 'description': 'Create animations and stories',
                'color': '#96CEB4', 'icon': 'film'},
        ]

        subjects = []
        for subject_data in subjects_data:
            subject, created = Subject.objects.get_or_create(
                name=subject_data['name'],
                defaults=subject_data
            )
            subjects.append(subject)
            if created:
                self.stdout.write(f'Created subject: {subject.name}')

        # Create grades
        grades_data = [
            {'name': 'Lớp 1-3', 'description': 'Dành cho học sinh tiểu học', 'order': 1},
            {'name': 'Lớp 4-6', 'description': 'Dành cho học sinh tiểu học cao', 'order': 2},
            {'name': 'Lớp 7-9',
                'description': 'Dành cho học sinh trung học cơ sở', 'order': 3},
            {'name': 'Lớp 10-12',
                'description': 'Dành cho học sinh trung học phổ thông', 'order': 4},
        ]

        grades = []
        for grade_data in grades_data:
            grade, created = Grade.objects.get_or_create(
                name=grade_data['name'],
                defaults=grade_data
            )
            grades.append(grade)
            if created:
                self.stdout.write(f'Created grade: {grade.name}')

        # Create teacher user
        teacher_user, created = User.objects.get_or_create(
            username='teacher1',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Nguyễn',
                'last_name': 'Giáo Viên',
                'is_active': True,
            }
        )
        if created:
            teacher_user.set_password('password123')
            teacher_user.save()
            self.stdout.write(f'Created teacher user: {teacher_user.username}')

        # Set teacher profile
        teacher_profile = teacher_user.profile
        teacher_profile.is_teacher = True
        teacher_profile.save()

        # Create student user
        student_user, created = User.objects.get_or_create(
            username='student1',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Trần',
                'last_name': 'Học Sinh',
                'is_active': True,
            }
        )
        if created:
            student_user.set_password('password123')
            student_user.save()
            self.stdout.write(f'Created student user: {student_user.username}')

        # Set student profile
        student_profile = student_user.profile
        student_profile.is_student = True
        student_profile.save()

        # Create sample courses
        courses_data = [
            {
                'title': 'Scratch Programming for Beginners',
                'description': 'Learn the basics of programming with Scratch. Perfect for kids and beginners.',
                'subject': subjects[0],
                'grade': grades[0],
                'difficulty': 'beginner',
                'teacher': teacher_user,
                'price': 500000,
                'original_price': 700000,
                'duration': '4 tuần',
                'estimated_time': 480,  # 8 hours
                'objectives': ['Understand basic programming concepts', 'Create simple animations', 'Build interactive stories'],
                'prerequisites': ['Basic computer skills', 'Interest in programming'],
                'features': ['Video lessons', 'Interactive exercises', 'Project-based learning'],
                'has_certificate': True,
                'is_published': True,
                'status': 'published',
            },
            {
                'title': 'Advanced Game Development with TurboWarp',
                'description': 'Create complex games using TurboWarp advanced features.',
                'subject': subjects[1],
                'grade': grades[2],
                'difficulty': 'advanced',
                'teacher': teacher_user,
                'price': 800000,
                'original_price': 1000000,
                'duration': '6 tuần',
                'estimated_time': 720,  # 12 hours
                'objectives': ['Master TurboWarp features', 'Create complex games', 'Understand game design principles'],
                'prerequisites': ['Basic Scratch knowledge', 'Programming experience'],
                'features': ['Advanced tutorials', 'Game design workshops', 'Portfolio projects'],
                'has_certificate': True,
                'is_published': True,
                'status': 'published',
            },
            {
                'title': 'Animation Storytelling',
                'description': 'Create engaging animations and tell stories through code.',
                'subject': subjects[3],
                'grade': grades[1],
                'difficulty': 'intermediate',
                'teacher': teacher_user,
                'price': 0,  # Free course
                'original_price': 0,
                'duration': '3 tuần',
                'estimated_time': 360,  # 6 hours
                'objectives': ['Create smooth animations', 'Tell stories through code', 'Design characters'],
                'prerequisites': ['Basic Scratch knowledge'],
                'features': ['Animation techniques', 'Character design', 'Story development'],
                'has_certificate': True,
                'is_published': True,
                'status': 'published',
            }
        ]

        courses = []
        for course_data in courses_data:
            course, created = Course.objects.get_or_create(
                title=course_data['title'],
                defaults=course_data
            )
            courses.append(course)
            if created:
                self.stdout.write(f'Created course: {course.title}')

        # Create sample lessons for each course
        for course in courses:
            lessons_data = [
                {
                    'title': f'Introduction to {course.subject.name}',
                    'description': 'Getting started with the basics',
                    'content_type': 'video',
                    'video_url': 'https://www.youtube.com/watch?v=example',
                    'duration': 30,
                    'order': 1,
                    'is_preview': True,
                },
                {
                    'title': 'Basic Concepts',
                    'description': 'Understanding fundamental concepts',
                    'content_type': 'text',
                    'content': '<h2>Basic Concepts</h2><p>In this lesson, we will cover...</p>',
                    'duration': 45,
                    'order': 2,
                },
                {
                    'title': 'Hands-on Practice',
                    'description': 'Practice what you have learned',
                    'content_type': 'video',
                    'video_url': 'https://www.youtube.com/watch?v=example2',
                    'duration': 60,
                    'order': 3,
                },
            ]

            for lesson_data in lessons_data:
                lesson, created = Lesson.objects.get_or_create(
                    course=course,
                    title=lesson_data['title'],
                    defaults=lesson_data
                )
                if created:
                    self.stdout.write(
                        f'Created lesson: {lesson.title} for {course.title}')

        # Create sample quizzes
        for course in courses:
            quiz, created = Quiz.objects.get_or_create(
                course=course,
                title=f'{course.title} - Quiz 1',
                defaults={
                    'description': 'Test your knowledge',
                    'duration': 15,
                    'max_attempts': 3,
                    'passing_score': 70.0,
                    'order': 1,
                }
            )
            if created:
                self.stdout.write(f'Created quiz: {quiz.title}')

                # Create sample questions
                questions_data = [
                    {
                        'question_type': 'multiple_choice',
                        'question_text': 'What is the main purpose of Scratch?',
                        'explanation': 'Scratch is designed to teach programming concepts.',
                        'points': 10.0,
                        'order': 1,
                        'options': ['Teaching programming', 'Playing games', 'Drawing pictures', 'Writing documents'],
                        'correct_answers': [0],
                    },
                    {
                        'question_type': 'true_false',
                        'question_text': 'Scratch uses visual blocks instead of text code.',
                        'explanation': 'Yes, Scratch uses drag-and-drop blocks.',
                        'points': 5.0,
                        'order': 2,
                        'options': ['True', 'False'],
                        'correct_answers': [0],
                    },
                ]

                for question_data in questions_data:
                    question, created = Question.objects.get_or_create(
                        quiz=quiz,
                        order=question_data['order'],
                        defaults=question_data
                    )
                    if created:
                        self.stdout.write(
                            f'Created question: {question.question_text[:50]}...')

        # Create sample assignments
        for course in courses:
            assignment, created = Assignment.objects.get_or_create(
                course=course,
                title=f'{course.title} - Final Project',
                defaults={
                    'description': 'Create a final project demonstrating your skills',
                    'assignment_type': 'turbowarp',
                    'required_features': ['User interaction', 'Animation', 'Sound effects'],
                    'grading_criteria': ['Creativity', 'Technical implementation', 'User experience'],
                    'starter_project_url': 'https://turbowarp.org/editor',
                    'max_score': 100.0,
                    'allow_late_submission': True,
                    'max_attempts': 3,
                    'order': 1,
                }
            )
            if created:
                self.stdout.write(f'Created assignment: {assignment.title}')

        self.stdout.write(
            self.style.SUCCESS(
                'Successfully created sample data for e-learning system!')
        )
        self.stdout.write('You can now:')
        self.stdout.write(
            '1. Login as teacher: username=teacher1, password=password123')
        self.stdout.write(
            '2. Login as student: username=student1, password=password123')
        self.stdout.write('3. Access the API endpoints at /api/elearning/')
