from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Avg, Su<PERSON>, Max
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated

from .models import (
    Subject, Grade, Course, Enrollment, Lesson, LessonProgress,
    Quiz, Question, QuestionBank, QuizAttempt, QuizAnswer, Assignment, AssignmentSubmission,
    CourseReview, Payment, Certificate
)
from .serializers import (
    SubjectSerializer, GradeSerializer, CourseListSerializer, CourseDetailSerializer,
    CourseCreateUpdateSerializer, LessonSerializer, LessonCreateUpdateSerializer,
    LessonProgressSerializer, EnrollmentSerializer, EnrollmentCreateSerializer,
    QuizSerializer, QuizCreateUpdateSerializer, QuestionSerializer, QuestionCreateUpdateSerializer,
    QuestionBankSerializer, QuestionBankCreateUpdateSerializer,
    QuizAttemptSerializer, QuizAnswerSerializer, AssignmentSerializer, AssignmentCreateUpdateSerializer,
    AssignmentSubmissionSerializer, AssignmentSubmissionCreateUpdateSerializer,
    CourseReviewSerializer, CourseReviewCreateUpdateSerializer, PaymentSerializer, PaymentCreateSerializer,
    CertificateSerializer, TeacherDashboardSerializer,
    StudentDashboardSerializer, CourseAnalyticsSerializer, CourseContentSerializer,
    StudentProgressSerializer
)


# ==================== COMMON VIEWS ====================

class SubjectListView(APIView):
    """List all subjects - Public endpoint"""
    permission_classes = []  # Public access for course browsing

    def get(self, request):
        subjects = Subject.objects.filter(is_active=True).order_by('name')
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data)


class GradeListView(APIView):
    """List all grades - Public endpoint"""
    permission_classes = []  # Public access for course browsing

    def get(self, request):
        grades = Grade.objects.filter(is_active=True).order_by('order')
        serializer = GradeSerializer(grades, many=True)
        return Response(serializer.data)


# ==================== TEACHER VIEWS ====================

class TeacherCourseListView(APIView):
    """Teacher's course management"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all courses for the current teacher"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to access teacher resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        courses = Course.objects.filter(
            teacher=request.user).order_by('-created_at')
        serializer = CourseListSerializer(
            courses, many=True, context={'request': request})
        return Response(serializer.data)

    def post(self, request):
        """Create a new course"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to create courses."},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = CourseCreateUpdateSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            course = serializer.save()
            response_serializer = CourseDetailSerializer(
                course, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TeacherCourseDetailView(APIView):
    """Teacher's course detail management"""
    permission_classes = [IsAuthenticated]

    def get_object(self, pk, user):
        return get_object_or_404(Course, pk=pk, teacher=user)

    def get(self, request, pk):
        """Get course details"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to access teacher resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = self.get_object(pk, request.user)
        serializer = CourseDetailSerializer(
            course, context={'request': request})
        return Response(serializer.data)

    def put(self, request, pk):
        """Update course"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to update courses."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = self.get_object(pk, request.user)
        serializer = CourseCreateUpdateSerializer(
            course, data=request.data, context={'request': request})
        if serializer.is_valid():
            course = serializer.save()
            response_serializer = CourseDetailSerializer(
                course, context={'request': request})
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        """Delete course"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete courses."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = self.get_object(pk, request.user)
        course.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherCoursePublishView(APIView):
    """Publish/unpublish course"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, pk):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to publish courses."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=pk, teacher=request.user)
        is_published = request.data.get('is_published', False)

        course.is_published = is_published
        course.status = 'published' if is_published else 'draft'
        if is_published and not course.published_at:
            course.published_at = timezone.now()
        course.save()

        serializer = CourseDetailSerializer(
            course, context={'request': request})
        return Response(serializer.data)


class TeacherCourseContentView(APIView):
    """Manage course content (lessons, quizzes, assignments)"""
    permission_classes = [IsAuthenticated]

    def get(self, request, pk):
        """Get all course content"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to access teacher resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=pk, teacher=request.user)

        lessons = course.lessons.all().order_by('order')
        quizzes = course.quizzes.all().order_by('order')
        assignments = course.assignments.all().order_by('order')

        data = {
            'lessons': LessonSerializer(lessons, many=True, context={'request': request}).data,
            'quizzes': QuizSerializer(quizzes, many=True, context={'request': request}).data,
            'assignments': AssignmentSerializer(assignments, many=True, context={'request': request}).data,
        }

        return Response(data)


class TeacherQuizView(APIView):
    """Manage course quizzes"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk):
        """Create a new quiz"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to create quizzes."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)

        serializer = QuizCreateUpdateSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            quiz = serializer.save(course=course)
            response_serializer = QuizSerializer(
                quiz, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, course_pk, quiz_pk):
        """Update quiz"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to update quizzes."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        quiz = get_object_or_404(Quiz, pk=quiz_pk, course=course)

        serializer = QuizCreateUpdateSerializer(
            quiz, data=request.data, context={'request': request})
        if serializer.is_valid():
            quiz = serializer.save()
            response_serializer = QuizSerializer(
                quiz, context={'request': request})
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, course_pk, quiz_pk):
        """Delete quiz"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete quizzes."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        quiz = get_object_or_404(Quiz, pk=quiz_pk, course=course)
        quiz.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherAssignmentView(APIView):
    """Manage course assignments"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk):
        """Create a new assignment"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to create assignments."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)

        serializer = AssignmentCreateUpdateSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            assignment = serializer.save(course=course)
            response_serializer = AssignmentSerializer(
                assignment, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, course_pk, assignment_pk):
        """Update assignment"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to update assignments."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        assignment = get_object_or_404(
            Assignment, pk=assignment_pk, course=course)

        serializer = AssignmentCreateUpdateSerializer(
            assignment, data=request.data, context={'request': request})
        if serializer.is_valid():
            assignment = serializer.save()
            response_serializer = AssignmentSerializer(
                assignment, context={'request': request})
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, course_pk, assignment_pk):
        """Delete assignment"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete assignments."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        assignment = get_object_or_404(
            Assignment, pk=assignment_pk, course=course)
        assignment.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherQuestionBankView(APIView):
    """Manage question bank"""
    permission_classes = [IsAuthenticated]

    def get(self, request, question_pk=None):
        """Get all questions from teacher's question bank or specific question"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to access question bank."},
                status=status.HTTP_403_FORBIDDEN
            )

        if question_pk:
            # Get specific question
            question = get_object_or_404(
                QuestionBank, pk=question_pk, teacher=request.user)
            serializer = QuestionBankSerializer(
                question, context={'request': request})
            return Response(serializer.data)
        else:
            # Get all questions from teacher's question bank
            questions = QuestionBank.objects.filter(teacher=request.user)

            # Filter by subject and grade if provided
            subject_id = request.query_params.get('subject')
            grade_id = request.query_params.get('grade')
            difficulty = request.query_params.get('difficulty')
            question_type = request.query_params.get('type')

            if subject_id:
                questions = questions.filter(subject_id=subject_id)
            if grade_id:
                questions = questions.filter(grade_id=grade_id)
            if difficulty:
                questions = questions.filter(difficulty=difficulty)
            if question_type:
                questions = questions.filter(question_type=question_type)

            serializer = QuestionBankSerializer(
                questions, many=True, context={'request': request})
            return Response(serializer.data)

    def post(self, request):
        """Create a new question in question bank"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to create questions."},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = QuestionBankCreateUpdateSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            question = serializer.save(teacher=request.user)
            response_serializer = QuestionBankSerializer(
                question, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, question_pk):
        """Update a question in question bank"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to update questions."},
                status=status.HTTP_403_FORBIDDEN
            )

        question = get_object_or_404(
            QuestionBank, pk=question_pk, teacher=request.user)
        serializer = QuestionBankCreateUpdateSerializer(
            question, data=request.data, context={'request': request})
        if serializer.is_valid():
            question = serializer.save()
            response_serializer = QuestionBankSerializer(
                question, context={'request': request})
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, question_pk):
        """Delete a question from question bank"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete questions."},
                status=status.HTTP_403_FORBIDDEN
            )

        question = get_object_or_404(
            QuestionBank, pk=question_pk, teacher=request.user)
        question.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherSubjectListView(APIView):
    """Get list of all subjects for teachers"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all subjects"""
        subjects = Subject.objects.all().order_by('name')
        serializer = SubjectSerializer(subjects, many=True)
        return Response(serializer.data)


class TeacherGradeListView(APIView):
    """Get list of all grades for teachers"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get all grades"""
        grades = Grade.objects.all().order_by('name')
        serializer = GradeSerializer(grades, many=True)
        return Response(serializer.data)

    def delete(self, request, question_pk):
        """Delete question"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete questions."},
                status=status.HTTP_403_FORBIDDEN
            )

        question = get_object_or_404(
            Question, pk=question_pk, quiz__course__teacher=request.user)
        question.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherLessonView(APIView):
    """Manage course lessons"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk):
        """Create a new lesson"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to create lessons."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)

        serializer = LessonCreateUpdateSerializer(
            data=request.data, context={'request': request})
        if serializer.is_valid():
            lesson = serializer.save(course=course)
            response_serializer = LessonSerializer(
                lesson, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, course_pk, lesson_pk):
        """Update lesson"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to update lessons."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        lesson = get_object_or_404(Lesson, pk=lesson_pk, course=course)

        serializer = LessonCreateUpdateSerializer(
            lesson, data=request.data, context={'request': request})
        if serializer.is_valid():
            lesson = serializer.save()
            response_serializer = LessonSerializer(
                lesson, context={'request': request})
            return Response(response_serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, course_pk, lesson_pk):
        """Delete lesson"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to delete lessons."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(Course, pk=course_pk, teacher=request.user)
        lesson = get_object_or_404(Lesson, pk=lesson_pk, course=course)
        lesson.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TeacherDashboardView(APIView):
    """Teacher dashboard statistics"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_teacher:
            return Response(
                {"detail": "You don't have permission to access teacher resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate dashboard statistics
        courses = Course.objects.filter(teacher=request.user)
        total_courses = courses.count()
        published_courses = courses.filter(is_published=True).count()
        draft_courses = courses.filter(is_published=False).count()

        enrollments = Enrollment.objects.filter(course__teacher=request.user)
        total_enrollments = enrollments.count()
        total_students = enrollments.values('student').distinct().count()

        payments = Payment.objects.filter(
            course__teacher=request.user, status='completed')
        total_revenue = sum(payment.amount for payment in payments)

        # Get recent enrollments
        recent_enrollments = enrollments.order_by('-enrolled_at')[:5]

        # Get popular courses
        popular_courses = courses.annotate(
            enrollment_count=Count('enrollments')
        ).order_by('-enrollment_count')[:5]

        data = {
            'total_courses': total_courses,
            'published_courses': published_courses,
            'draft_courses': draft_courses,
            'total_students': total_students,
            'total_enrollments': total_enrollments,
            'total_revenue': total_revenue,
            'avg_course_rating': 4.5,  # Calculate from reviews
            'recent_enrollments': EnrollmentSerializer(recent_enrollments, many=True, context={'request': request}).data,
            'popular_courses': CourseListSerializer(popular_courses, many=True, context={'request': request}).data,
        }

        return Response(data)


# ==================== STUDENT VIEWS ====================

class StudentAvailableCoursesView(APIView):
    """Get available courses for students"""
    permission_classes = []  # Temporarily remove authentication for testing

    def get(self, request):
        """Get available courses with filters"""
        # Temporarily comment out authentication check for testing
        # if not hasattr(request.user, 'profile') or not request.user.profile.is_student:
        #     return Response(
        #         {"detail": "You don't have permission to access student resources."},
        #         status=status.HTTP_403_FORBIDDEN
        #     )

        courses = Course.objects.filter(is_published=True, status='published')

        # Apply filters
        subject = request.query_params.get('subject')
        grade = request.query_params.get('grade')
        difficulty = request.query_params.get('difficulty')
        price_range = request.query_params.get('price_range')
        search = request.query_params.get('search')

        if subject:
            courses = courses.filter(subject__name=subject)
        if grade:
            courses = courses.filter(grade__name=grade)
        if difficulty:
            courses = courses.filter(difficulty=difficulty)
        if price_range:
            if price_range == 'free':
                courses = courses.filter(price=0)
            elif price_range == 'under_500k':
                courses = courses.filter(price__lt=500000)
            elif price_range == '500k_1m':
                courses = courses.filter(price__gte=500000, price__lt=1000000)
            elif price_range == 'over_1m':
                courses = courses.filter(price__gte=1000000)
        if search:
            courses = courses.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(subject__name__icontains=search)
            )

        courses = courses.order_by('-created_at')
        serializer = CourseListSerializer(
            courses, many=True, context={'request': request})
        return Response(serializer.data)


class StudentEnrolledCoursesView(APIView):
    """Get student's enrolled courses"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get enrolled courses"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_student:
            return Response(
                {"detail": "You don't have permission to access student resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        enrollments = Enrollment.objects.filter(
            student=request.user).order_by('-enrolled_at')
        serializer = EnrollmentSerializer(
            enrollments, many=True, context={'request': request})
        return Response(serializer.data)


class StudentCourseDetailView(APIView):
    """Get course details for students - Public for browsing"""
    permission_classes = []  # Public access for course browsing

    def get(self, request, pk):
        """Get course details"""
        course = get_object_or_404(
            Course, pk=pk, is_published=True, status='published')

        # Check if student is enrolled (only if authenticated)
        enrollment = None
        is_enrolled = False
        if request.user.is_authenticated and hasattr(request.user, 'profile'):
            enrollment = Enrollment.objects.filter(
                student=request.user, course=course).first()
            is_enrolled = enrollment is not None

        serializer = CourseDetailSerializer(
            course, context={'request': request})
        data = serializer.data
        data['is_enrolled'] = is_enrolled
        data['enrollment'] = EnrollmentSerializer(
            enrollment, context={'request': request}).data if enrollment else None

        return Response(data)


class StudentCoursePurchaseView(APIView):
    """Purchase/enroll in a course"""
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        """Purchase a course"""
        if not hasattr(request.user, 'profile') or not request.user.profile.is_student:
            return Response(
                {"detail": "You don't have permission to purchase courses."},
                status=status.HTTP_403_FORBIDDEN
            )

        course = get_object_or_404(
            Course, pk=pk, is_published=True, status='published')

        # Check if already enrolled
        if Enrollment.objects.filter(student=request.user, course=course).exists():
            return Response(
                {"detail": "You are already enrolled in this course."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check course capacity
        if course.enrolled_count >= course.max_students:
            return Response(
                {"detail": "This course is full."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create enrollment
        enrollment_data = {
            'course': course.id,
            'paid_amount': course.price,
            'payment_method': request.data.get('payment_method', 'credit_card')
        }

        enrollment_serializer = EnrollmentCreateSerializer(
            data=enrollment_data, context={'request': request})

        if enrollment_serializer.is_valid():
            enrollment = enrollment_serializer.save()

            # Create payment record if not free
            if course.price > 0:
                payment_data = {
                    'course': course.id,
                    'amount': course.price,
                    'original_amount': course.original_price or course.price,
                    'discount_amount': (course.original_price or course.price) - course.price,
                    'payment_method': request.data.get('payment_method', 'credit_card'),
                    'external_transaction_id': request.data.get('transaction_id', ''),
                    'payment_gateway': request.data.get('payment_gateway', ''),
                }

                payment_serializer = PaymentCreateSerializer(
                    data=payment_data, context={'request': request})
                if payment_serializer.is_valid():
                    payment = payment_serializer.save(enrollment=enrollment)
                    # In real implementation, process payment here
                    payment.status = 'completed'
                    payment.completed_at = timezone.now()
                    payment.save()

                    enrollment.payment_date = timezone.now()
                    enrollment.save()

            return Response(
                EnrollmentSerializer(enrollment, context={
                                     'request': request}).data,
                status=status.HTTP_201_CREATED
            )

        return Response(enrollment_serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class StudentCourseContentView(APIView):
    """Get course content - Public for demo/testing"""
    permission_classes = []  # Public access for testing

    def get(self, request, pk):
        """Get course content"""
        course = get_object_or_404(
            Course, pk=pk, is_published=True, status='published')

        # Get course content
        lessons = course.lessons.all().order_by('order')
        quizzes = course.quizzes.all().order_by('order')
        assignments = course.assignments.all().order_by('order')

        # Check enrollment status (optional if authenticated)
        is_enrolled = False
        enrollment = None
        if request.user.is_authenticated and hasattr(request.user, 'profile'):
            enrollment = Enrollment.objects.filter(
                student=request.user, course=course, status='active').first()
            is_enrolled = enrollment is not None

        data = {
            'course': {
                'id': course.id,
                'title': course.title,
                'description': course.description,
                'syllabus': course.syllabus,
                'is_enrolled': is_enrolled
            },
            'lessons': LessonSerializer(lessons, many=True, context={'request': request}).data,
            'quizzes': QuizSerializer(quizzes, many=True, context={'request': request}).data,
            'assignments': AssignmentSerializer(assignments, many=True, context={'request': request}).data,
        }

        return Response(data)


class StudentDashboardView(APIView):
    """Student dashboard statistics"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        if not hasattr(request.user, 'profile') or not request.user.profile.is_student:
            return Response(
                {"detail": "You don't have permission to access student resources."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate dashboard statistics
        enrollments = Enrollment.objects.filter(student=request.user)
        enrolled_courses = enrollments.filter(status='active').count()
        completed_courses = enrollments.filter(status='completed').count()
        in_progress_courses = enrolled_courses - completed_courses

        certificates = Certificate.objects.filter(student=request.user).count()
        total_time_spent = sum(
            enrollment.total_time_spent for enrollment in enrollments)

        # Get recommended courses (simple logic - same subjects as enrolled courses)
        enrolled_subjects = enrollments.values_list(
            'course__subject', flat=True).distinct()
        recommended_courses = Course.objects.filter(
            subject__in=enrolled_subjects,
            is_published=True,
            status='published'
        ).exclude(
            id__in=enrollments.values_list('course', flat=True)
        )[:5]

        data = {
            'enrolled_courses': enrolled_courses,
            'completed_courses': completed_courses,
            'in_progress_courses': in_progress_courses,
            'total_certificates': certificates,
            'total_time_spent': total_time_spent,
            'avg_score': 85.0,  # Calculate from quiz attempts
            'recent_activities': [],  # Implement activity tracking
            'recommended_courses': CourseListSerializer(recommended_courses, many=True, context={'request': request}).data,
        }

        return Response(data)


# ==================== STUDENT PROGRESS VIEWS ====================

class StudentCourseProgressView(APIView):
    """Get overall course progress for a student"""
    permission_classes = [IsAuthenticated]

    def get(self, request, course_pk):
        """Get course progress"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            course = enrollment.course

            # Get lesson progress
            lesson_progress = LessonProgress.objects.filter(
                student=request.user,
                lesson__course=course,
                enrollment=enrollment
            ).select_related('lesson')

            # Get quiz attempts
            quiz_attempts = QuizAttempt.objects.filter(
                student=request.user,
                quiz__course=course
            ).select_related('quiz')

            return Response({
                'course': {
                    'id': course.id,
                    'title': course.title,
                },
                'lesson_progress': LessonProgressSerializer(lesson_progress, many=True).data,
                'quiz_attempts': QuizAttemptSerializer(quiz_attempts, many=True).data,
                'enrollment': EnrollmentSerializer(enrollment).data,
            })

        except Exception as e:
            return Response(
                {"detail": f"Error loading course progress: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentLessonProgressView(APIView):
    """Update lesson progress"""
    permission_classes = [IsAuthenticated]

    def patch(self, request, course_pk, lesson_pk):
        """Update lesson progress"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            # Get lesson
            lesson = get_object_or_404(
                Lesson, pk=lesson_pk, course_id=course_pk)

            # Get or create lesson progress
            lesson_progress, created = LessonProgress.objects.get_or_create(
                student=request.user,
                lesson=lesson,
                enrollment=enrollment,
                defaults={
                    'time_spent': 0,
                    'completion_percentage': 0.0,
                    'is_completed': False
                }
            )

            # Update progress
            time_spent = request.data.get(
                'time_spent', lesson_progress.time_spent)
            completion_percentage = request.data.get(
                'completion_percentage', lesson_progress.completion_percentage)
            is_completed = request.data.get(
                'is_completed', lesson_progress.is_completed)

            lesson_progress.time_spent = max(
                lesson_progress.time_spent, time_spent)  # Don't decrease time
            lesson_progress.completion_percentage = max(
                lesson_progress.completion_percentage, completion_percentage)
            lesson_progress.is_completed = is_completed or lesson_progress.is_completed
            lesson_progress.last_accessed = timezone.now()

            if lesson_progress.is_completed and not lesson_progress.completed_at:
                lesson_progress.completed_at = timezone.now()

            lesson_progress.save()

            return Response(LessonProgressSerializer(lesson_progress).data)

        except Exception as e:
            return Response(
                {"detail": f"Error updating lesson progress: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentCourseCompleteView(APIView):
    """Complete course and generate certificate"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk):
        """Mark course as completed and generate certificate"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            course = enrollment.course

            # Check if course is already completed
            if enrollment.status == 'completed':
                return Response(
                    {"detail": "Course already completed"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify all required lessons are completed
            total_lessons = course.lessons.filter(is_required=True).count()
            completed_lessons = LessonProgress.objects.filter(
                student=request.user,
                lesson__course=course,
                lesson__is_required=True,
                is_completed=True,
                enrollment=enrollment
            ).count()

            if completed_lessons < total_lessons:
                return Response(
                    {
                        "detail": f"Course not ready for completion. {completed_lessons}/{total_lessons} required lessons completed."
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Calculate final score from quiz attempts
            quiz_attempts = QuizAttempt.objects.filter(
                student=request.user,
                quiz__course=course,
                status='submitted'
            ).values('quiz').annotate(
                best_score=Max('score')
            )

            total_score = 0
            quiz_count = 0
            for attempt in quiz_attempts:
                if attempt['best_score'] is not None:
                    total_score += attempt['best_score']
                    quiz_count += 1

            final_score = total_score / quiz_count if quiz_count > 0 else 100.0

            # Update enrollment status
            enrollment.status = 'completed'
            enrollment.completed_at = timezone.now()
            enrollment.progress_percentage = 100.0
            enrollment.completed_lessons = completed_lessons
            enrollment.save()

            # Create certificate if course has certificate and doesn't exist
            certificate = None
            if course.has_certificate:
                certificate, created = Certificate.objects.get_or_create(
                    student=request.user,
                    course=course,
                    enrollment=enrollment,
                    defaults={
                        'final_score': final_score,
                        'completion_date': timezone.now(),
                        'is_verified': True
                    }
                )

            response_data = {
                'enrollment': EnrollmentSerializer(enrollment).data,
                'certificate': CertificateSerializer(certificate).data if certificate else None,
                'completion_stats': {
                    'total_lessons': total_lessons,
                    'completed_lessons': completed_lessons,
                    'final_score': final_score,
                    'completion_date': enrollment.completed_at.isoformat(),
                    'total_time_spent': enrollment.total_time_spent
                }
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"detail": f"Error completing course: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentQuizAttemptView(APIView):
    """Start quiz attempt"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk, quiz_pk):
        """Start a new quiz attempt"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            # Get quiz
            quiz = get_object_or_404(Quiz, pk=quiz_pk, course_id=course_pk)

            # Create quiz attempt
            attempt = QuizAttempt.objects.create(
                student=request.user,
                quiz=quiz,
                started_at=timezone.now()
            )

            return Response(QuizAttemptSerializer(attempt).data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"detail": f"Error starting quiz attempt: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentQuizSubmitView(APIView):
    """Submit quiz answers"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk, quiz_pk, attempt_pk):
        """Submit quiz answers and calculate score"""
        try:
            # Get quiz attempt
            attempt = get_object_or_404(
                QuizAttempt,
                pk=attempt_pk,
                student=request.user,
                quiz_id=quiz_pk,
                quiz__course_id=course_pk
            )

            if attempt.completed_at:
                return Response(
                    {"detail": "Quiz attempt already completed"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            answers_data = request.data.get('answers', {})
            score = request.data.get('score', 0)

            # Save answers
            for question_id, selected_answers in answers_data.items():
                question = get_object_or_404(
                    Question, pk=question_id, quiz=attempt.quiz)

                # Determine if answer is correct
                if isinstance(selected_answers, list):
                    # Multiple choice
                    is_correct = set(selected_answers) == set(
                        question.correct_answers)
                else:
                    # Single choice
                    is_correct = selected_answers in question.correct_answers

                QuizAnswer.objects.create(
                    attempt=attempt,
                    question=question,
                    selected_answers=selected_answers if isinstance(
                        selected_answers, list) else [selected_answers],
                    is_correct=is_correct
                )

            # Update attempt
            attempt.score = score
            attempt.is_passed = score >= (attempt.quiz.passing_score or 70)
            attempt.completed_at = timezone.now()
            attempt.save()

            return Response(QuizAttemptSerializer(attempt).data)

        except Exception as e:
            return Response(
                {"detail": f"Error submitting quiz: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentQuizCompleteView(APIView):
    """Mark quiz as completed (for progress tracking)"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk, quiz_pk):
        """Mark quiz as completed"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            # Get quiz
            quiz = get_object_or_404(Quiz, pk=quiz_pk, course_id=course_pk)

            # Check if user has passed the quiz
            best_attempt = QuizAttempt.objects.filter(
                student=request.user,
                quiz=quiz,
                status='submitted'
            ).order_by('-score').first()

            if not best_attempt or not best_attempt.is_passed:
                return Response(
                    {"detail": "Quiz must be passed before marking as completed"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Return completion status
            return Response({
                'quiz_id': quiz.id,
                'is_completed': True,
                'best_score': best_attempt.score,
                'completion_date': best_attempt.submitted_at.isoformat()
            })

        except Exception as e:
            return Response(
                {"detail": f"Error marking quiz complete: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# ==================== ASSIGNMENT SUBMISSION VIEWS ====================

class StudentAssignmentSubmitView(APIView):
    """Submit assignment"""
    permission_classes = [IsAuthenticated]

    def post(self, request, course_pk, assignment_pk):
        """Submit assignment file"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            # Get assignment
            assignment = get_object_or_404(
                Assignment, pk=assignment_pk, course_id=course_pk)

            # Get uploaded file
            uploaded_file = request.FILES.get('file')
            if not uploaded_file:
                return Response(
                    {"detail": "No file uploaded"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check file size (10MB limit)
            if uploaded_file.size > 10 * 1024 * 1024:
                return Response(
                    {"detail": "File size exceeds 10MB limit"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create assignment submission
            # TODO: Create AssignmentSubmission model
            submission_data = {
                'id': 1,  # Placeholder
                'assignment_id': assignment.id,
                'student_id': request.user.id,
                'file_name': uploaded_file.name,
                'file_size': uploaded_file.size,
                'submitted_at': timezone.now().isoformat(),
                'status': 'submitted',
                'grade': None,
                'feedback': None
            }

            return Response(submission_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"detail": f"Error submitting assignment: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class StudentAssignmentSubmissionsView(APIView):
    """Get assignment submissions"""
    permission_classes = [IsAuthenticated]

    def get(self, request, course_pk, assignment_pk):
        """Get assignment submissions for student"""
        try:
            # Check enrollment
            enrollment = get_object_or_404(
                Enrollment,
                student=request.user,
                course_id=course_pk,
                status='active'
            )

            # Get assignment
            assignment = get_object_or_404(
                Assignment, pk=assignment_pk, course_id=course_pk)

            # TODO: Get actual submissions from AssignmentSubmission model
            submissions = []

            return Response(submissions)

        except Exception as e:
            return Response(
                {"detail": f"Error fetching assignment submissions: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
