#!/usr/bin/env python3
"""
Script to seed sample e-learning data for testing
Run this script to populate the database with sample courses, subjects, grades, etc.
"""

from elearning_api.models import Subject, Grade, Course, Lesson, Quiz, Assignment, Enrollment
from account_api.models import UserProfile
from django.contrib.auth.models import User
import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bee_stem_solutions.settings')
django.setup()


def create_sample_data():
    print("🌱 Starting to seed e-learning data...")

    # 1. Create Subjects
    print("📚 Creating subjects...")
    subjects_data = [
        {'name': 'Lập trình', 'description': '<PERSON><PERSON><PERSON> kh<PERSON>a học lập trình và công nghệ'},
        {'name': '<PERSON><PERSON> học', 'description': '<PERSON><PERSON> học từ cơ bản đến nâng cao'},
        {'name': '<PERSON><PERSON>t lý', 'description': 'Vật lý lý thuyết và thực hành'},
        {'name': 'Hóa học', 'description': 'Hóa học cơ bản và nâng cao'},
        {'name': 'Tiếng Anh', 'description': 'Tiếng Anh giao tiếp và học thuật'},
        {'name': 'STEM', 'description': 'Khoa học, Công nghệ, Kỹ thuật, Toán học tích hợp'},
    ]

    subjects = {}
    for subject_data in subjects_data:
        subject, created = Subject.objects.get_or_create(
            name=subject_data['name'],
            defaults={
                'description': subject_data['description'],
                'is_active': True
            }
        )
        subjects[subject.name] = subject
        print(f"  ✅ {subject.name} {'(created)' if created else '(exists)'}")

    # 2. Create Grades
    print("🎓 Creating grades...")
    grades_data = [
        {'name': 'Lớp 6', 'order': 6},
        {'name': 'Lớp 7', 'order': 7},
        {'name': 'Lớp 8', 'order': 8},
        {'name': 'Lớp 9', 'order': 9},
        {'name': 'Lớp 10', 'order': 10},
        {'name': 'Lớp 11', 'order': 11},
        {'name': 'Lớp 12', 'order': 12},
        {'name': 'Đại học', 'order': 13},
        {'name': 'Tất cả', 'order': 0},
    ]

    grades = {}
    for grade_data in grades_data:
        grade, created = Grade.objects.get_or_create(
            name=grade_data['name'],
            defaults={
                'order': grade_data['order'],
                'is_active': True
            }
        )
        grades[grade.name] = grade
        print(f"  ✅ {grade.name} {'(created)' if created else '(exists)'}")

    # 3. Create Teacher User
    print("👨‍🏫 Creating teacher user...")
    teacher_user, created = User.objects.get_or_create(
        username='teacher_stem',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Nguyễn',
            'last_name': 'Văn A',
            'is_active': True
        }
    )
    if created:
        teacher_user.set_password('teacher123')
        teacher_user.save()

    teacher_profile, created = UserProfile.objects.get_or_create(
        user=teacher_user,
        defaults={
            'is_teacher': True,
            'is_student': False,
            'phone': '0123456789',
            'address': 'Hà Nội, Việt Nam'
        }
    )
    print(
        f"  ✅ Teacher: {teacher_user.get_full_name()} {'(created)' if created else '(exists)'}")

    # 4. Create Student User
    print("👨‍🎓 Creating student user...")
    student_user, created = User.objects.get_or_create(
        username='student_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Trần',
            'last_name': 'Thị B',
            'is_active': True
        }
    )
    if created:
        student_user.set_password('student123')
        student_user.save()

    student_profile, created = UserProfile.objects.get_or_create(
        user=student_user,
        defaults={
            'is_teacher': False,
            'is_student': True,
            'phone': '0987654321',
            'address': 'TP.HCM, Việt Nam'
        }
    )
    print(
        f"  ✅ Student: {student_user.get_full_name()} {'(created)' if created else '(exists)'}")

    # 5. Create Sample Courses
    print("📖 Creating sample courses...")
    courses_data = [
        {
            'title': 'Lập trình Python cơ bản - STEM',
            'description': 'Học lập trình Python từ cơ bản đến nâng cao với phương pháp STEM',
            'subject': subjects['STEM'],
            'grade': grades['Lớp 9'],
            'difficulty': 'beginner',
            'price': Decimal('0'),
            'original_price': Decimal('500000'),
            'duration': '3 tháng',
            'estimated_time': 180,
            'status': 'published',
            'is_published': True,
            'objectives': [
                'Hiểu các khái niệm cơ bản của STEM',
                'Áp dụng kiến thức vào các dự án thực tế',
                'Phát triển kỹ năng tư duy logic và sáng tạo',
                'Hoàn thành các thử thách STEM thú vị',
                'Xây dựng portfolio dự án ấn tượng',
                'Chuẩn bị cho sự nghiệp trong lĩnh vực STEM'
            ],
            'prerequisites': [
                'Không cần kiến thức lập trình trước',
                'Máy tính có kết nối internet',
                'Tinh thần học hỏi và thực hành',
                'Thời gian dành ra ít nhất 2-3 giờ/tuần'
            ],
            'syllabus': [
                {
                    'title': '🚀 Giới thiệu khóa học STEM',
                    'lessons': [
                        {'title': 'Chào mừng đến với thế giới STEM',
                            'duration': 5, 'type': 'video'},
                        {'title': 'Cài đặt môi trường học tập',
                            'duration': 8, 'type': 'video'},
                        {'title': 'Quiz: Kiểm tra hiểu biết cơ bản',
                            'duration': 10, 'type': 'quiz'}
                    ]
                },
                {
                    'title': '🐍 Python cơ bản',
                    'lessons': [
                        {'title': 'Biến và kiểu dữ liệu',
                            'duration': 15, 'type': 'video'},
                        {'title': 'Cấu trúc điều khiển',
                            'duration': 20, 'type': 'video'},
                        {'title': 'Bài tập thực hành',
                            'duration': 30, 'type': 'assignment'}
                    ]
                },
                {
                    'title': '🔬 Dự án STEM thực tế',
                    'lessons': [
                        {'title': 'Xây dựng máy tính đơn giản',
                            'duration': 45, 'type': 'project'},
                        {'title': 'Mô phỏng thí nghiệm vật lý',
                            'duration': 40, 'type': 'project'},
                        {'title': 'Trình bày dự án',
                            'duration': 20, 'type': 'presentation'}
                    ]
                }
            ]
        },
        {
            'title': 'Toán học nâng cao lớp 12',
            'description': 'Ôn tập và nâng cao kiến thức toán học cho kỳ thi THPT',
            'subject': subjects['Toán học'],
            'grade': grades['Lớp 12'],
            'difficulty': 'intermediate',
            'price': Decimal('300000'),
            'original_price': Decimal('500000'),
            'duration': '4 tháng',
            'estimated_time': 240,
            'status': 'published',
            'is_published': True,
            'objectives': [
                'Nắm vững kiến thức toán học lớp 12',
                'Giải thành thạo các dạng bài thi THPT',
                'Phát triển tư duy logic toán học',
                'Đạt điểm cao trong kỳ thi THPT',
                'Áp dụng toán học vào thực tế',
                'Chuẩn bị tốt cho đại học'
            ],
            'prerequisites': [
                'Hoàn thành chương trình toán lớp 11',
                'Kiến thức cơ bản về đại số và hình học',
                'Máy tính bỏ túi khoa học',
                'Thời gian học ít nhất 4-5 giờ/tuần'
            ],
            'syllabus': [
                {
                    'title': '📊 Hàm số và đồ thị',
                    'lessons': [
                        {'title': 'Khái niệm hàm số',
                            'duration': 20, 'type': 'video'},
                        {'title': 'Các loại hàm số cơ bản',
                            'duration': 25, 'type': 'video'},
                        {'title': 'Bài tập hàm số',
                            'duration': 40, 'type': 'assignment'}
                    ]
                },
                {
                    'title': '🔢 Giải tích',
                    'lessons': [
                        {'title': 'Đạo hàm và ứng dụng',
                            'duration': 30, 'type': 'video'},
                        {'title': 'Tích phân cơ bản',
                            'duration': 35, 'type': 'video'},
                        {'title': 'Bài tập giải tích',
                            'duration': 45, 'type': 'assignment'}
                    ]
                },
                {
                    'title': '📐 Hình học không gian',
                    'lessons': [
                        {'title': 'Khối đa diện', 'duration': 25, 'type': 'video'},
                        {'title': 'Mặt cầu và khối cầu',
                            'duration': 20, 'type': 'video'},
                        {'title': 'Đề thi thử THPT', 'duration': 90, 'type': 'exam'}
                    ]
                }
            ]
        },
        {
            'title': 'Vật lý cơ bản với thí nghiệm',
            'description': 'Khóa học vật lý cơ bản với thí nghiệm thực tế và mô phỏng',
            'subject': subjects['Vật lý'],
            'grade': grades['Lớp 10'],
            'difficulty': 'beginner',
            'price': Decimal('0'),
            'original_price': Decimal('400000'),
            'duration': '2 tháng',
            'estimated_time': 120,
            'status': 'published',
            'is_published': True,
            'objectives': [
                'Hiểu các định luật vật lý cơ bản',
                'Thực hiện thí nghiệm vật lý đơn giản',
                'Ứng dụng vật lý vào đời sống',
                'Phát triển tư duy khoa học',
                'Giải các bài tập vật lý cơ bản',
                'Chuẩn bị cho các môn vật lý nâng cao'
            ],
            'prerequisites': [
                'Kiến thức toán học cơ bản',
                'Tò mò về thế giới tự nhiên',
                'Dụng cụ thí nghiệm đơn giản',
                'Thời gian thực hành ít nhất 2 giờ/tuần'
            ],
            'syllabus': [
                {
                    'title': '⚡ Cơ học cơ bản',
                    'lessons': [
                        {'title': 'Chuyển động thẳng đều',
                            'duration': 18, 'type': 'video'},
                        {'title': 'Lực và chuyển động',
                            'duration': 22, 'type': 'video'},
                        {'title': 'Thí nghiệm con lắc đơn',
                            'duration': 35, 'type': 'experiment'}
                    ]
                },
                {
                    'title': '🔋 Điện học',
                    'lessons': [
                        {'title': 'Dòng điện và điện áp',
                            'duration': 20, 'type': 'video'},
                        {'title': 'Định luật Ohm', 'duration': 15, 'type': 'video'},
                        {'title': 'Thí nghiệm mạch điện',
                            'duration': 40, 'type': 'experiment'}
                    ]
                },
                {
                    'title': '🌊 Sóng và ánh sáng',
                    'lessons': [
                        {'title': 'Tính chất sóng', 'duration': 25, 'type': 'video'},
                        {'title': 'Khúc xạ ánh sáng',
                            'duration': 20, 'type': 'video'},
                        {'title': 'Dự án khoa học',
                            'duration': 60, 'type': 'project'}
                    ]
                }
            ]
        }
    ]

    courses = []
    for course_data in courses_data:
        course, created = Course.objects.get_or_create(
            title=course_data['title'],
            teacher=teacher_user,
            defaults=course_data
        )
        courses.append(course)
        print(f"  ✅ {course.title} {'(created)' if created else '(exists)'}")

        # Create sample lessons for each course
        if created:
            create_sample_lessons(course)

    # 6. Create Sample Enrollments
    print("📝 Creating sample enrollments...")
    for i, course in enumerate(courses[:2]):  # Enroll in first 2 courses
        enrollment, created = Enrollment.objects.get_or_create(
            student=student_user,
            course=course,
            defaults={
                'payment_method': 'free' if course.price == 0 else 'credit_card',
                'paid_amount': course.price,
                'progress_percentage': 25.0 + (i * 20),
                'completed_lessons': 3 + i,
                'status': 'active'
            }
        )
        print(
            f"  ✅ Enrollment: {course.title} {'(created)' if created else '(exists)'}")

    print("🎉 Sample e-learning data created successfully!")
    print("\n📋 Summary:")
    print(f"  - Subjects: {Subject.objects.count()}")
    print(f"  - Grades: {Grade.objects.count()}")
    print(f"  - Courses: {Course.objects.count()}")
    print(f"  - Lessons: {Lesson.objects.count()}")
    print(f"  - Enrollments: {Enrollment.objects.count()}")
    print(f"\n🔑 Test accounts:")
    print(f"  - Teacher: teacher_stem / teacher123")
    print(f"  - Student: student_test / student123")


def create_sample_lessons(course):
    """Create sample lessons for a course"""

    # Create lessons based on course subject
    if course.subject.name == 'STEM':
        lessons_data = [
            {
                'title': 'Giới thiệu về STEM',
                'description': 'Tìm hiểu về phương pháp giáo dục STEM tích hợp Khoa học, Công nghệ, Kỹ thuật và Toán học',
                'content_type': 'text',
                'content': '''Chào mừng bạn đến với khóa học STEM!

STEM là viết tắt của:
- Science (Khoa học)
- Technology (Công nghệ)
- Engineering (Kỹ thuật)
- Mathematics (Toán học)

Phương pháp STEM giúp học sinh:
✓ Phát triển tư duy logic và sáng tạo
✓ Kết nối kiến thức với thực tế
✓ Làm việc nhóm hiệu quả
✓ Giải quyết vấn đề một cách khoa học''',
                'duration': 15,
                'order': 1,
                'is_preview': True
            },
            {
                'title': 'Video: Lập trình Python cơ bản',
                'description': 'Video hướng dẫn các khái niệm cơ bản của lập trình Python và ứng dụng trong STEM',
                'content_type': 'video',
                'video_url': 'https://www.youtube.com/watch?v=fXmuxPDxbGg',  # YouTube URL example
                'content': '''Bài học về lập trình Python cơ bản:

1. Biến và kiểu dữ liệu:
   - Số nguyên (int): age = 15
   - Số thực (float): height = 1.75
   - Chuỗi (string): name = "Học sinh"
   - Boolean: is_student = True

2. Cấu trúc điều khiển:
   - If/else statements
   - For loops
   - While loops

3. Ứng dụng trong STEM:
   - Tính toán khoa học
   - Mô phỏng thí nghiệm
   - Phân tích dữ liệu''',
                'duration': 25,
                'order': 2
            },
            {
                'title': 'Dự án STEM thực tế',
                'description': 'Thực hiện dự án STEM tích hợp kiến thức đã học',
                'content_type': 'assignment',
                'content': '''Dự án: Xây dựng máy tính đơn giản bằng Python

Yêu cầu:
1. Tạo chương trình máy tính có thể thực hiện các phép tính cơ bản (+, -, *, /)
2. Thêm tính năng tính căn bậc hai và lũy thừa
3. Tạo giao diện đơn giản cho người dùng
4. Viết báo cáo về quá trình thực hiện (500-1000 từ)

Tiêu chí đánh giá:
- Chương trình chạy đúng (40%)
- Giao diện thân thiện (20%)
- Code sạch và có comment (20%)
- Báo cáo chi tiết (20%)''',
                'duration': 60,
                'order': 3
            },
            {
                'title': 'Tài liệu: Hướng dẫn STEM',
                'description': 'Tài liệu PDF hướng dẫn chi tiết về phương pháp STEM',
                'content_type': 'file',
                'content': '''Tài liệu này bao gồm:

📚 Nội dung chính:
- Lý thuyết STEM cơ bản
- Các phương pháp giảng dạy
- Ví dụ thực tế
- Bài tập thực hành

📝 Hướng dẫn sử dụng:
1. Đọc kỹ phần lý thuyết
2. Thực hành theo ví dụ
3. Làm bài tập cuối chương
4. Áp dụng vào dự án thực tế''',
                'duration': 30,
                'order': 4
            }
        ]
    elif course.subject.name == 'Toán học':
        lessons_data = [
            {
                'title': 'Hàm số và đồ thị',
                'description': 'Tìm hiểu về khái niệm hàm số và cách vẽ đồ thị',
                'content_type': 'video',
                'content': '''Bài học về Hàm số và Đồ thị:

1. Khái niệm hàm số:
   - Hàm số là quy tắc đặt tương ứng mỗi giá trị x với một giá trị y duy nhất
   - Ký hiệu: y = f(x)

2. Các loại hàm số cơ bản:
   - Hàm bậc nhất: y = ax + b
   - Hàm bậc hai: y = ax² + bx + c
   - Hàm lũy thừa: y = xⁿ
   - Hàm mũ: y = aˣ

3. Đồ thị hàm số:
   - Cách vẽ đồ thị
   - Tính chất của đồ thị
   - Ứng dụng thực tế''',
                'duration': 30,
                'order': 1,
                'is_preview': True
            },
            {
                'title': 'Đạo hàm và ứng dụng',
                'description': 'Học về đạo hàm và các ứng dụng trong giải tích',
                'content_type': 'video',
                'content': '''Bài học về Đạo hàm:

1. Định nghĩa đạo hàm:
   - Đạo hàm là giới hạn của tỷ số gia số
   - f'(x) = lim[h→0] [f(x+h) - f(x)]/h

2. Công thức đạo hàm cơ bản:
   - (xⁿ)' = n.xⁿ⁻¹
   - (sin x)' = cos x
   - (cos x)' = -sin x
   - (eˣ)' = eˣ

3. Ứng dụng đạo hàm:
   - Tìm cực trị hàm số
   - Khảo sát hàm số
   - Bài toán tối ưu''',
                'duration': 35,
                'order': 2
            }
        ]
    else:  # Vật lý hoặc môn khác
        lessons_data = [
            {
                'title': 'Cơ học cơ bản',
                'description': 'Tìm hiểu về các định luật cơ học Newton',
                'content_type': 'video',
                'content': '''Bài học về Cơ học cơ bản:

1. Ba định luật Newton:

   Định luật I (Quán tính):
   - Vật đứng yên sẽ tiếp tục đứng yên
   - Vật chuyển động thẳng đều sẽ tiếp tục chuyển động thẳng đều
   - Trừ khi có lực tác dụng

   Định luật II (Động lực học):
   - F = ma
   - Lực tỷ lệ thuận với gia tốc

   Định luật III (Tác dụng - Phản tác dụng):
   - Lực tác dụng và phản tác dụng bằng nhau về độ lớn, ngược chiều''',
                'duration': 25,
                'order': 1,
                'is_preview': True
            },
            {
                'title': 'Thí nghiệm con lắc đơn',
                'description': 'Thực hiện thí nghiệm con lắc đơn và phân tích kết quả',
                'content_type': 'assignment',
                'content': '''Thí nghiệm: Con lắc đơn

Mục tiêu:
- Xác định gia tốc trọng trường g
- Nghiên cứu dao động điều hòa

Dụng cụ cần thiết:
- Dây treo (dài khoảng 1m)
- Quả nặng nhỏ
- Thước đo
- Đồng hồ bấm giây

Các bước thực hiện:
1. Treo quả nặng vào dây, tạo con lắc đơn
2. Đo chiều dài dây l
3. Kéo con lắc lệch góc nhỏ (< 10°) rồi thả
4. Đo thời gian 10 dao động, tính chu kỳ T
5. Lặp lại với các chiều dài khác nhau''',
                'duration': 45,
                'order': 2
            }
        ]

    for lesson_data in lessons_data:
        lesson_data['course'] = course
        Lesson.objects.get_or_create(
            course=course,
            title=lesson_data['title'],
            defaults=lesson_data
        )


if __name__ == '__main__':
    create_sample_data()
