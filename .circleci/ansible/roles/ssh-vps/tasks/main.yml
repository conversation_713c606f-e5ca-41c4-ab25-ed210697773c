- name: remove dependencies that are no longer required
  become: yes
  apt:
    autoremove: yes

- name: "pull latest code"
  shell: |
    cd /root/workspace/bee-stem-solutions
    git stash
    git checkout develop
    git pull

- name: "[BE] Deploy Backend"
  shell: |
    cd /root/workspace/bee-stem-solutions/backend
    source .venv/bin/activate
    python3 -m pip install -r requirements.txt
    python3 manage.py makemigrations
    python3 manage.py migrate
    systemctl restart gunicorn

- name: "[FE] Deploy Frontend"
  shell: |
    cd /root/workspace/bee-stem-solutions/frontend
    rm -rf dist node_modules package-lock.json
    npm install --legacy-peer-deps
    npm run build
    systemctl restart nginx
